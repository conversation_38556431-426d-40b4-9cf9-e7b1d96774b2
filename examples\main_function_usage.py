"""
新的main函数使用示例
展示如何使用参数化的main函数进行公司调研
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import main, research_single_company, research_multiple_companies
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def example_single_company():
    """示例1: 调研单个公司"""
    print("\n=== 示例1: 调研单个公司 ===")
    
    company_name = "TriSalus"
    print(f"正在调研公司: {company_name}")
    
    try:
        result = research_single_company(company_name)
        
        print(f"调研状态: {result.get('status', 'unknown')}")
        if result.get('status') == 'completed':
            print(f"官网URL: {result.get('base_url', 'N/A')}")
            print(f"投资者关系页面数量: {len(result.get('investor_relations_urls', []))}")
            print(f"XPath规则数量: {len(result.get('news_xpath_rules', []))}")
        else:
            print(f"调研失败: {result.get('error', '未知错误')}")
            
    except Exception as e:
        print(f"调研异常: {e}")

def example_multiple_companies_basic():
    """示例2: 批量调研多个公司（基本用法）"""
    print("\n=== 示例2: 批量调研多个公司（基本用法） ===")
    
    companies = ["TriSalus", "Apple", "Microsoft"]
    
    try:
        # 使用默认参数
        results = main(companies)
        
        print(f"调研完成，共处理 {len(results)} 个公司")
        for result in results:
            status = result.get('status', 'unknown')
            company = result.get('company_name', 'Unknown')
            print(f"  {company}: {status}")
            
    except Exception as e:
        print(f"批量调研异常: {e}")

def example_multiple_companies_custom():
    """示例3: 批量调研多个公司（自定义参数）"""
    print("\n=== 示例3: 批量调研多个公司（自定义参数） ===")
    
    companies = ["TriSalus", "Google"]
    
    try:
        # 使用自定义参数
        results = main(
            companies=companies,
            output_file="custom_research_results.json",  # 自定义输出文件名
            save_results=True,                           # 保存结果
            quiet_mode=False,                           # 非静默模式
            print_summary_info=True                     # 打印摘要
        )
        
        print(f"自定义调研完成，结果已保存到 custom_research_results.json")
        
        # 处理结果
        successful_companies = [r for r in results if r.get('status') == 'completed']
        failed_companies = [r for r in results if r.get('status') != 'completed']
        
        print(f"成功: {len(successful_companies)} 个")
        print(f"失败: {len(failed_companies)} 个")
        
    except Exception as e:
        print(f"自定义调研异常: {e}")

def example_quiet_mode():
    """示例4: 静默模式调研"""
    print("\n=== 示例4: 静默模式调研 ===")
    
    companies = ["TriSalus"]
    
    try:
        # 静默模式，不保存文件，不打印摘要
        results = main(
            companies=companies,
            save_results=False,      # 不保存到文件
            quiet_mode=True,         # 静默模式
            print_summary_info=False # 不打印摘要
        )
        
        print("静默模式调研完成")
        print(f"返回结果数量: {len(results)}")
        
        # 手动处理结果
        for result in results:
            company = result.get('company_name')
            status = result.get('status')
            print(f"  {company}: {status}")
            
    except Exception as e:
        print(f"静默模式调研异常: {e}")

def example_error_handling():
    """示例5: 错误处理"""
    print("\n=== 示例5: 错误处理 ===")
    
    # 测试空公司列表
    try:
        results = main([])
        print("空列表调研完成")
    except Exception as e:
        print(f"空列表调研异常（预期）: {e}")
    
    # 测试无效公司名称
    try:
        results = main([""])
        print("无效公司名调研完成")
    except Exception as e:
        print(f"无效公司名调研异常（预期）: {e}")

def example_programmatic_usage():
    """示例6: 编程式使用"""
    print("\n=== 示例6: 编程式使用 ===")
    
    # 模拟一个业务流程
    companies_to_research = ["TriSalus", "Tesla", "Amazon"]
    
    print("开始业务流程...")
    
    try:
        # 第一步：调研公司
        print("步骤1: 执行公司调研")
        results = main(
            companies=companies_to_research,
            output_file="business_research.json",
            save_results=True,
            quiet_mode=True,  # 静默模式，减少日志输出
            print_summary_info=False
        )
        
        # 第二步：分析结果
        print("步骤2: 分析调研结果")
        successful_results = []
        failed_results = []
        
        for result in results:
            if result.get('status') == 'completed':
                successful_results.append(result)
            else:
                failed_results.append(result)
        
        # 第三步：生成报告
        print("步骤3: 生成业务报告")
        print(f"  总计调研: {len(results)} 个公司")
        print(f"  成功调研: {len(successful_results)} 个公司")
        print(f"  失败调研: {len(failed_results)} 个公司")
        
        if successful_results:
            print("  成功的公司:")
            for result in successful_results:
                company = result.get('company_name')
                ir_count = len(result.get('investor_relations_urls', []))
                xpath_count = len(result.get('news_xpath_rules', []))
                print(f"    - {company}: {ir_count}个IR页面, {xpath_count}个XPath规则")
        
        if failed_results:
            print("  失败的公司:")
            for result in failed_results:
                company = result.get('company_name')
                error = result.get('error', '未知错误')
                print(f"    - {company}: {error}")
        
        print("业务流程完成")
        
    except Exception as e:
        print(f"业务流程异常: {e}")

def main_demo():
    """主演示函数"""
    print("AI Tools Find News - 新main函数使用示例")
    print("=" * 60)
    
    # 运行所有示例
    examples = [
        example_single_company,
        example_multiple_companies_basic,
        example_multiple_companies_custom,
        example_quiet_mode,
        example_error_handling,
        example_programmatic_usage
    ]
    
    for i, example_func in enumerate(examples, 1):
        try:
            example_func()
        except Exception as e:
            print(f"示例 {i} 执行异常: {e}")
        
        if i < len(examples):
            print("\n" + "-" * 40)
    
    print("\n" + "=" * 60)
    print("所有示例演示完成！")
    
    print("\n使用说明:")
    print("1. 单个公司调研: research_single_company('公司名')")
    print("2. 批量调研: main(['公司1', '公司2'], ...)")
    print("3. 自定义参数: main(companies, output_file, save_results, quiet_mode)")
    print("4. 命令行兼容: 仍可使用 main_cli() 函数")

if __name__ == "__main__":
    main_demo()
