"""
DrissionPage备用方案演示
展示如何使用新的重试机制处理防御性网站
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.web_scraper import WebScraperService
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def demo_normal_website():
    """演示正常网站的抓取"""
    print("\n=== 演示1: 正常网站抓取 ===")
    
    web_scraper = WebScraperService()
    
    # 测试一个正常的网站
    test_url = "https://httpbin.org/html"
    print(f"正在抓取: {test_url}")
    
    try:
        content = web_scraper.fetch_page(test_url)
        if content:
            print(f"✅ 抓取成功，内容长度: {len(content)}")
            print(f"内容预览: {content[:200]}...")
        else:
            print("❌ 抓取失败")
    except Exception as e:
        print(f"❌ 抓取异常: {e}")
    finally:
        web_scraper.close()

def demo_js_website():
    """演示需要JavaScript渲染的网站抓取"""
    print("\n=== 演示2: JavaScript渲染网站抓取 ===")
    
    web_scraper = WebScraperService()
    
    # 测试一个需要JS渲染的网站（示例）
    test_url = "https://quotes.toscrape.com/js/"
    print(f"正在使用DrissionPage抓取JS网站: {test_url}")
    
    try:
        # 使用专门的JS抓取方法
        content = web_scraper.fetch_page_with_js(test_url, wait_selector=".quote", wait_time=5.0)
        if content:
            print(f"✅ JS网站抓取成功，内容长度: {len(content)}")
            # 检查是否包含动态加载的内容
            if "quote" in content.lower():
                print("✅ 成功获取到JavaScript渲染的内容")
            else:
                print("⚠️ 可能未完全加载JavaScript内容")
        else:
            print("❌ JS网站抓取失败")
    except Exception as e:
        print(f"❌ JS网站抓取异常: {e}")
    finally:
        web_scraper.close()

def demo_fallback_mechanism():
    """演示备用机制的工作原理"""
    print("\n=== 演示3: 备用机制工作原理 ===")
    
    web_scraper = WebScraperService()
    
    # 测试一个可能有防御机制的网站
    test_urls = [
        "https://httpbin.org/status/403",  # 模拟403错误
        "https://httpbin.org/delay/10",    # 模拟超时
        "https://httpbin.org/html"         # 正常网站
    ]
    
    for url in test_urls:
        print(f"\n正在测试: {url}")
        try:
            content = web_scraper.fetch_page(url)
            if content:
                print(f"✅ 抓取成功，内容长度: {len(content)}")
            else:
                print("❌ 抓取失败，可能触发了备用机制")
        except Exception as e:
            print(f"❌ 抓取异常: {e}")
    
    web_scraper.close()

def demo_configuration_info():
    """显示配置信息"""
    print("\n=== 配置信息 ===")
    
    from src.config import Config
    
    print(f"最大重试次数: {Config.MAX_RETRIES}")
    print(f"请求超时时间: {Config.SCRAPING_TIMEOUT}秒")
    print(f"重试延迟时间: {Config.REQUEST_DELAY}秒")
    
    print("\n重试策略:")
    print("1. 第1-2次: 使用requests库")
    print("2. 第3次: 使用DrissionPage作为备用方案")
    print("3. DrissionPage支持JavaScript渲染和反爬虫绕过")

def main():
    """主函数"""
    print("DrissionPage备用方案演示")
    print("=" * 50)
    
    # 显示配置信息
    demo_configuration_info()
    
    # 演示正常网站抓取
    demo_normal_website()
    
    # 演示JavaScript网站抓取
    demo_js_website()
    
    # 演示备用机制
    demo_fallback_mechanism()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    
    print("\n使用建议:")
    print("1. 对于普通网站，直接使用 fetch_page() 方法")
    print("2. 对于已知需要JS渲染的网站，使用 fetch_page_with_js() 方法")
    print("3. 系统会自动在requests失败时启用DrissionPage备用方案")
    print("4. 记得在使用完毕后调用 close() 方法清理资源")

if __name__ == "__main__":
    main()
