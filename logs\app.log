2025-07-02 17:01:12,596 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:01:12,596 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:01:12,640 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:01:12,640 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:01:19,278 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: company
2025-07-02 17:01:37,144 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: company
2025-07-02 17:03:13,407 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:03:13,407 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:03:13,583 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:03:13,583 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:03:13,583 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:03:13,587 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:03:13,587 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:17,332 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:17,334 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:17,334 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:17,334 - src.utils.retry_decorator - WARNING - 函数 _search_official_website 第 1 次尝试失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   , 1.0秒后重试
2025-07-02 17:03:18,337 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:21,022 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:21,023 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:21,023 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:21,023 - src.utils.retry_decorator - WARNING - 函数 _search_official_website 第 2 次尝试失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   , 2.0秒后重试
2025-07-02 17:03:23,029 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:25,818 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:25,820 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.utils.retry_decorator - ERROR - 函数 _search_official_website 重试 2 次后仍然失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:05:03,769 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:05:03,769 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:05:03,816 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:05:03,816 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:05:03,816 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:05:03,818 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:05:03,818 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:05:06,385 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:05:06,386 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:09,904 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 210
2025-07-02 17:05:09,905 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:09,905 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:05:09,905 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:05:09,905 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:11,065 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:11,066 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:14,291 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 226
2025-07-02 17:05:14,291 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:14,291 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:14,291 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:05:15,304 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:16,255 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:16,256 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:19,675 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 244
2025-07-02 17:05:19,676 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:19,676 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:19,676 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:05:21,686 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:22,643 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:22,644 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:31,470 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 222
2025-07-02 17:05:31,470 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:31,470 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:31,470 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:31,470 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:07,892 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:06:07,892 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:06:07,938 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:06:07,938 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:06:07,938 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:06:07,940 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:06:07,940 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:06:10,710 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:06:10,711 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:13,220 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 244
2025-07-02 17:06:13,220 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:13,220 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:06:13,220 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:06:13,220 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:14,160 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:14,162 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:16,655 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:06:16,655 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:16,655 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:16,655 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:06:17,664 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:18,602 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:18,603 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:20,541 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:06:20,541 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:20,541 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:20,542 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:06:22,553 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:23,516 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:23,517 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:25,985 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:06:25,985 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:25,985 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:25,985 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:25,985 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:48,849 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:06:48,849 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:06:48,896 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:06:48,896 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:06:48,896 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:06:48,898 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:06:48,898 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:06:51,338 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:06:51,339 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:54,048 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 320
2025-07-02 17:06:54,049 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:54,049 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性。其他结果虽然也与Precigen相关，但均为子页面或投资者页面，不符合首页的要求。"
}
```
2025-07-02 17:06:54,049 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:06:54,049 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:06:54,049 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:06:54,049 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:54,998 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:54,999 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:58,497 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 246
2025-07-02 17:06:58,497 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:58,497 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有发现与'财务信息'、'年报'、'公告'等相关词汇的链接。由于HTML内容似乎是损坏或乱码，无法进行有效的链接提取和分析。"
}
```
2025-07-02 17:06:58,497 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:06:58,497 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:58,497 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:06:59,507 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:00,466 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:07:00,467 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:02,637 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 238
2025-07-02 17:07:02,637 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:02,637 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能识别出任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有找到与'财务信息'、'年报'、'公告'等相关词汇的链接。由于内容为乱码，无法进行有效的链接提取和分析。"
}
```
2025-07-02 17:07:02,638 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:02,638 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:02,638 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:07:04,646 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:05,620 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:07:05,621 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:07,335 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:07:07,335 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:07,335 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:07:07,335 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:07,335 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:07,335 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:07,336 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:33,602 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:07:33,602 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:07:33,644 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:07:33,644 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:07:33,644 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:07:33,646 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:07:33,647 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:07:36,162 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:07:36,164 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:38,467 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 264
2025-07-02 17:07:38,467 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:38,467 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且该域名为.com，符合主流域名标准。此外，该URL没有包含任何子页面，直接指向公司的首页，且内容描述与公司业务高度相关，显示出其作为官方网站的权威性。"
}
```
2025-07-02 17:07:38,467 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:38,467 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:07:38,467 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:07:38,467 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:39,428 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:39,428 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:41,546 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 222
2025-07-02 17:07:41,546 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:41,546 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:07:41,547 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:41,547 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:41,547 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:07:42,559 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:43,527 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:43,528 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:45,886 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:07:45,886 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:45,886 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。由于内容被损坏或无法解析，无法提取有效的链接信息。"
}
```
2025-07-02 17:07:45,886 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:45,886 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:45,886 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:07:47,892 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:48,851 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:48,852 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:51,225 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:07:51,225 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:51,225 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:07:51,225 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:51,225 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:51,227 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:51,227 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:08:13,209 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:08:13,210 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:08:14,179 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:08:55,586 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:08:55,586 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:08:56,518 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:09:25,224 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:09:25,224 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:09:26,141 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:10:12,403 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:10:12,403 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:10:12,454 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:10:12,454 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:10:12,454 - src.core.search_research - INFO - 开始调研公司: 苹果公司
2025-07-02 17:10:12,456 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:10:12,456 - src.services.google_search - INFO - 执行Google搜索: 苹果公司
2025-07-02 17:10:15,151 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:10:15,152 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:18,290 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 304
2025-07-02 17:10:18,290 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:18,290 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://www.apple.com/",
  "confidence": 0.98,
  "reasoning": "选择https://www.apple.com/作为苹果公司的官方网站，因为该URL直接对应于苹果公司的主域名，且是以.com结尾的主流域名。该页面提供了苹果公司的所有产品和服务信息，符合官方网站的标准。虽然https://www.apple.com.cn/也是一个有效的官方网站，但它是针对中国大陆用户的子域名，优先选择了全球主站。其他结果如维基百科和百度百科等均为第三方平台，不符合官方网站的标准。"
}
```
2025-07-02 17:10:18,290 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:18,290 - src.core.search_research - INFO - 找到官网: https://www.apple.com/
2025-07-02 17:10:18,290 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:10:18,290 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:18,475 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:18,475 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:21,459 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 247
2025-07-02 17:10:21,459 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:21,459 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中搜索了包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，以及'财务信息'、'年报'、'公告'等相关词汇的链接。所有链接均未包含相关内容。"
}
```
2025-07-02 17:10:21,459 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:21,459 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:21,459 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:10:22,470 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:22,565 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:22,565 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:25,004 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 255
2025-07-02 17:10:25,004 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:25,004 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中检查了导航菜单、页脚和主要内容区域，但未发现包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有与'财务信息'、'年报'、'公告'等相关词汇的链接。"
}
```
2025-07-02 17:10:25,004 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:25,004 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:25,004 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:10:27,015 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:27,116 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:27,117 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:29,321 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 254
2025-07-02 17:10:29,322 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:29,322 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中检查了导航菜单、页脚和主要内容区域，未发现包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有与'财务信息'、'年报'、'公告'等相关词汇的链接。"
}
```
2025-07-02 17:10:29,322 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:29,322 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:29,322 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:29,322 - src.core.search_research - ERROR - 公司调研失败: 苹果公司, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:04,523 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:11:04,524 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:11:04,599 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:11:04,599 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:11:04,599 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:11:04,604 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:11:04,604 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:11:07,214 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:11:07,217 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:14,750 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 313
2025-07-02 17:11:14,750 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:14,750 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com域名，符合官方网站的标准。该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性和相关性。其他结果虽然也包含Precigen的相关信息，但均为子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:11:14,750 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:14,751 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:11:14,751 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:11:14,751 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:15,670 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:15,671 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:18,019 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:11:18,020 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:18,020 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:11:18,020 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:18,020 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:18,020 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:11:19,033 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:19,330 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:19,331 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:21,584 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:11:21,584 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:21,584 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'、'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:11:21,584 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:21,584 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:21,584 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:11:23,596 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:23,894 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:23,894 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:25,815 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:11:25,815 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:25,815 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:11:25,815 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:25,815 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:25,815 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:25,815 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:12:44,755 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:12:44,756 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:12:44,831 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:12:44,831 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:12:44,831 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:12:44,835 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:12:44,835 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:12:47,340 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:12:47,342 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:12:51,283 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 208
2025-07-02 17:12:51,284 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:12:51,284 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "该URL是Precigen的官方网站首页，标题明确标识为'Precigen: Home'，且域名为主流的.com后缀，符合官方网站的标准。其他结果虽然包含公司名称，但都是子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:12:51,284 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:12:51,284 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:12:51,284 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:12:51,285 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:13:15,852 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:13:15,853 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:13:15,930 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:13:15,930 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:13:15,930 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:13:15,934 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:13:15,934 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:13:19,148 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:13:19,150 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:13:21,451 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 297
2025-07-02 17:13:21,451 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:13:21,451 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com后缀，符合官方网站的标准。此外，该页面的内容描述与公司业务高度相关，进一步确认了其作为官方网站的权威性。其他结果虽然也包含Precigen的相关信息，但均为子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:13:21,452 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:13:21,452 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:13:21,452 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:13:21,452 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:13:22,372 - src.services.web_scraper - ERROR - 页面抓取过程中发生未知错误: local variable 'content' referenced before assignment, URL: https://precigen.com/
2025-07-02 17:13:22,373 - src.core.search_research - WARNING - 无法获取首页内容: https://precigen.com/
2025-07-02 17:13:22,373 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 无法获取首页内容: https://precigen.com/, 1.0秒后重试
2025-07-02 17:13:34,785 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:13:34,785 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:13:34,860 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:13:34,860 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:13:34,861 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:13:34,864 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:13:34,864 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:13:37,387 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:13:37,388 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:13:40,544 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 316
2025-07-02 17:13:40,544 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:13:40,545 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心信息，进一步确认了其作为官方网站的权威性。其他结果虽然也包含precigen.com的子页面，但优先选择首页以符合要求。"
}
```
2025-07-02 17:13:40,545 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:13:40,545 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:13:40,545 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:13:40,546 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:15:53,242 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:15:53,242 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:15:53,311 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:15:53,312 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:15:53,312 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:15:53,315 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:15:53,315 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:15:55,971 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:15:55,973 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:15:58,879 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 256
2025-07-02 17:15:58,879 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:15:58,880 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com后缀，符合公司名称的官方域名标准。此外，该链接是首页而非子页面，且没有涉及第三方平台，具有较高的权威性和相关性。"
}
```
2025-07-02 17:15:58,880 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:15:58,880 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:15:58,880 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:15:58,881 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:16:48,345 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:16:48,345 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:16:48,422 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:16:48,422 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:16:48,422 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:16:48,426 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:16:48,426 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:16:50,912 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:16:50,913 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:16:54,343 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 318
2025-07-02 17:16:54,344 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:16:54,344 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性。其他结果虽然也包含precigen.com的子页面，但优先选择首页以满足要求。"
}
```
2025-07-02 17:16:54,344 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:16:54,344 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:16:54,344 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:16:54,345 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:16:57,687 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 35557
2025-07-02 17:16:58,380 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:17:02,089 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 590
2025-07-02 17:17:02,089 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:17:02,089 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.precigen.com/",
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/stock",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ],
  "found_keywords": ["投资者", "Investor Relations"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，首先检查了HTML内容中的导航菜单和页脚部分，发现了多个与投资者关系相关的链接。所有链接均包含'投资者'或相关财务信息的关键词，且均为绝对链接。经过去重处理，最终列出了7个独特的投资者关系链接。"
}
```
2025-07-02 17:17:02,089 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:17:02,089 - src.core.search_research - INFO - 找到投资者关系页面: 7个
2025-07-02 17:17:02,090 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:17:02,090 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/
2025-07-02 17:17:02,090 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/
2025-07-02 17:17:32,839 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/
2025-07-02 17:18:03,930 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 3/3, URL: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.services.web_scraper - ERROR - 页面抓取最终超时: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - WARNING - 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - WARNING - 分析投资者关系页面失败: https://investors.precigen.com/, 错误: 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/pressreleases
2025-07-02 17:18:36,033 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:19:06,118 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/pressreleases
2025-07-02 17:19:37,214 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/pressreleases
2025-07-02 17:19:59,592 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:19:59,593 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:19:59,673 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:19:59,674 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:19:59,674 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:19:59,678 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:19:59,678 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:20:02,276 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:20:02,277 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:20:04,914 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 256
2025-07-02 17:20:04,914 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:20:04,914 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com域名，符合公司名称的官方域名标准。此外，该页面的内容描述与公司业务高度相关，进一步确认了其作为官方网站的权威性。"
}
```
2025-07-02 17:20:04,914 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:20:04,914 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:20:04,914 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:20:04,914 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:20:05,882 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 35557
2025-07-02 17:20:05,883 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:20:08,341 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 630
2025-07-02 17:20:08,341 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:20:08,341 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.precigen.com/",
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/stock",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ],
  "found_keywords": ["投资者", "Investor Relations"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，我搜索了包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接。所有相关链接均位于导航菜单中的'Investors'部分及其子链接中。所有链接均为绝对链接，且没有重复。置信度为0.95，因所有链接均清晰且直接指向投资者关系相关内容。"
}
```
2025-07-02 17:20:08,341 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 找到投资者关系页面: 7个
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/
2025-07-02 17:20:08,341 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/
2025-07-02 17:20:38,431 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/
2025-07-02 17:21:09,525 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 3/3, URL: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.services.web_scraper - ERROR - 页面抓取最终超时: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.core.search_research - WARNING - 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.core.search_research - WARNING - 分析投资者关系页面失败: https://investors.precigen.com/, 错误: 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:21:41,644 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/pressreleases
2025-07-02 17:21:41,644 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:21:48,621 - src.services.web_scraper - ERROR - 页面抓取请求异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')), URL: https://investors.precigen.com/pressreleases
2025-07-02 17:22:29,230 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/pressreleases
2025-07-02 17:31:37,677 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:37,678 - src.services.web_scraper - INFO - 开始抓取页面: https://example.com
2025-07-02 17:31:37,678 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://example.com
2025-07-02 17:31:37,678 - src.services.web_scraper - WARNING - requests页面抓取过程中发生未知错误: 模拟网络错误, URL: https://example.com
2025-07-02 17:31:38,679 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://example.com
2025-07-02 17:31:38,679 - src.services.web_scraper - WARNING - requests页面抓取过程中发生未知错误: 模拟网络错误, URL: https://example.com
2025-07-02 17:31:38,679 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://example.com
2025-07-02 17:31:38,680 - src.services.web_scraper - WARNING - DrissionPage页面抓取失败: https://example.com
2025-07-02 17:31:38,680 - src.services.web_scraper - ERROR - 所有重试方案均失败: https://example.com
2025-07-02 17:31:38,680 - __main__ - INFO - 所有方法失败测试通过
2025-07-02 17:31:38,680 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:38,680 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:31:38,680 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:31:38,681 - __main__ - INFO - DrissionPage延迟初始化测试通过
2025-07-02 17:31:38,681 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 17:31:38,681 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:38,681 - src.services.web_scraper - INFO - 使用DrissionPage抓取JS渲染页面: https://spa-example.com
2025-07-02 17:31:38,682 - src.services.web_scraper - INFO - DrissionPage JS页面抓取成功: https://spa-example.com, 内容长度: 32
2025-07-02 17:31:38,682 - __main__ - INFO - JS页面抓取测试通过
2025-07-02 17:31:38,682 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:38,682 - src.services.web_scraper - INFO - 开始抓取页面: https://httpbin.org/html
2025-07-02 17:31:38,682 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://httpbin.org/html
2025-07-02 17:31:40,712 - src.services.web_scraper - INFO - requests页面抓取成功: https://httpbin.org/html, 内容长度: 3705
2025-07-02 17:31:40,713 - __main__ - INFO - 正常requests请求测试通过
2025-07-02 17:31:40,713 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:40,714 - src.services.web_scraper - INFO - 开始抓取页面: https://example.com
2025-07-02 17:31:40,714 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://example.com
2025-07-02 17:31:40,714 - src.services.web_scraper - WARNING - requests页面抓取过程中发生未知错误: 模拟网络错误, URL: https://example.com
2025-07-02 17:31:41,724 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://example.com
2025-07-02 17:31:41,724 - src.services.web_scraper - WARNING - requests页面抓取过程中发生未知错误: 模拟网络错误, URL: https://example.com
2025-07-02 17:31:41,724 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://example.com
2025-07-02 17:31:41,724 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://example.com, 内容长度: 40
2025-07-02 17:31:41,724 - __main__ - INFO - requests失败DrissionPage成功测试通过
2025-07-02 17:31:41,725 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:41,725 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:31:41,725 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:31:41,725 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 17:31:41,725 - __main__ - INFO - 资源清理测试通过
2025-07-02 17:31:48,037 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:48,038 - src.services.web_scraper - INFO - 开始抓取页面: https://httpbin.org/html
2025-07-02 17:31:48,038 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://httpbin.org/html
2025-07-02 17:31:49,019 - src.services.web_scraper - INFO - requests页面抓取成功: https://httpbin.org/html, 内容长度: 3705
2025-07-02 17:31:49,020 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:49,020 - src.services.web_scraper - INFO - 使用DrissionPage抓取JS渲染页面: https://quotes.toscrape.com/js/
2025-07-02 17:31:49,346 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:31:49,346 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:31:49,346 - src.services.drission_scraper - INFO - 使用DrissionPage抓取JS渲染页面: https://quotes.toscrape.com/js/
2025-07-02 17:31:51,266 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 17:31:53,515 - src.services.drission_scraper - WARNING - 等待元素超时: .quote, 错误: 'ChromiumPageWaiter' object has no attribute 'ele_loaded'
2025-07-02 17:31:53,518 - src.services.drission_scraper - INFO - DrissionPage JS页面抓取成功: https://quotes.toscrape.com/js/, 内容长度: 8940
2025-07-02 17:31:53,521 - src.services.web_scraper - INFO - DrissionPage JS页面抓取成功: https://quotes.toscrape.com/js/, 内容长度: 3928
2025-07-02 17:31:54,552 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-02 17:31:54,552 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 17:31:54,552 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:31:54,552 - src.services.web_scraper - INFO - 开始抓取页面: https://httpbin.org/status/403
2025-07-02 17:31:54,553 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://httpbin.org/status/403
2025-07-02 17:31:55,549 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://httpbin.org/status/403
2025-07-02 17:31:56,554 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://httpbin.org/status/403
2025-07-02 17:31:57,551 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://httpbin.org/status/403
2025-07-02 17:31:57,551 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://httpbin.org/status/403
2025-07-02 17:31:57,551 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:31:57,551 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:31:57,551 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://httpbin.org/status/403
2025-07-02 17:31:58,424 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 17:32:39,102 - src.services.drission_scraper - WARNING - DrissionPage获取到空内容: https://httpbin.org/status/403
2025-07-02 17:32:39,103 - src.services.web_scraper - WARNING - DrissionPage页面抓取失败: https://httpbin.org/status/403
2025-07-02 17:32:39,103 - src.services.web_scraper - ERROR - 所有重试方案均失败: https://httpbin.org/status/403
2025-07-02 17:32:39,104 - src.services.web_scraper - INFO - 开始抓取页面: https://httpbin.org/delay/10
2025-07-02 17:32:39,104 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://httpbin.org/delay/10
2025-07-02 17:32:50,519 - src.services.web_scraper - INFO - requests页面抓取成功: https://httpbin.org/delay/10, 内容长度: 541
2025-07-02 17:32:50,520 - src.services.web_scraper - INFO - 开始抓取页面: https://httpbin.org/html
2025-07-02 17:32:50,520 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://httpbin.org/html
2025-07-02 17:32:51,522 - src.services.web_scraper - INFO - requests页面抓取成功: https://httpbin.org/html, 内容长度: 3705
2025-07-02 17:32:52,542 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-02 17:32:52,542 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 17:33:52,798 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:33:52,799 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:33:52,880 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:33:52,880 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:33:52,880 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:33:52,883 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:33:52,884 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:33:55,605 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:33:55,607 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:33:58,257 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 309
2025-07-02 17:33:58,257 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:33:58,257 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com域名，符合官方网站的标准。该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性和相关性。其他结果虽然也与Precigen相关，但均为子页面或投资者页面，不符合首页的要求。"
}
```
2025-07-02 17:33:58,257 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:33:58,257 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:33:58,257 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:33:58,258 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:33:58,258 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://precigen.com/
2025-07-02 17:33:59,256 - src.services.web_scraper - INFO - requests页面抓取成功: https://precigen.com/, 内容长度: 35557
2025-07-02 17:33:59,257 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:34:02,231 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 582
2025-07-02 17:34:02,231 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:34:02,231 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.precigen.com/",
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/stock",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ],
  "found_keywords": ["投资者", "Investor Relations"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，首先检查了HTML内容中的导航菜单和页脚部分，发现了多个与投资者关系相关的链接。这些链接包含了'投资者'和'财务信息'等关键词，符合识别标准。所有链接均为绝对链接，且没有重复项。"
}
```
2025-07-02 17:34:02,231 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:34:02,231 - src.core.search_research - INFO - 找到投资者关系页面: 7个
2025-07-02 17:34:02,231 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:34:02,231 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/
2025-07-02 17:34:02,231 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/
2025-07-02 17:34:02,231 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.precigen.com/
2025-07-02 17:34:36,215 - src.services.web_scraper - WARNING - requests页面抓取超时，尝试 1/2, URL: https://investors.precigen.com/
2025-07-02 17:34:37,218 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.precigen.com/
2025-07-02 17:35:07,311 - src.services.web_scraper - WARNING - requests页面抓取超时，尝试 2/2, URL: https://investors.precigen.com/
2025-07-02 17:35:07,311 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.precigen.com/
2025-07-02 17:35:08,299 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:35:08,299 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:35:08,299 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.precigen.com/
2025-07-02 17:35:09,286 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 17:35:43,706 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/, 内容长度: 149515
2025-07-02 17:35:43,750 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/, 内容长度: 37084
2025-07-02 17:35:43,750 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:35:48,252 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 881
2025-07-02 17:35:48,252 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:35:48,252 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--i-news')]//a[@class='post-box']",
    "//div[@class='nir-widget--content']//a[@class='post-box']",
    "//div[@class='latest-posts']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--i-news')]//a[@class='post-box']",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻列表位于class为'block--i-news'的div内，包含多个a标签，每个a标签代表一条新闻，包含新闻标题和链接。每条新闻的日期信息位于相应的article元素内，确保了信息的完整性。",
  "sample_links": [
    "https://investors.precigen.com/news-releases/news-release-details/precigen-reports-first-quarter-2025-financial-results-and",
    "https://investors.precigen.com/news-releases/news-release-details/precigen-and-recurrent-respiratory-papillomatosis-foundation-0",
    "https://investors.precigen.com/news-releases/news-release-details/precigen-reports-full-year-2024-financial-results-and-business"
  ]
}
```
2025-07-02 17:35:48,252 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:35:48,252 - src.core.search_research - INFO - 从 https://investors.precigen.com/ 提取到 3 个XPath规则
2025-07-02 17:35:49,268 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/pressreleases
2025-07-02 17:35:49,268 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:35:49,268 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.precigen.com/pressreleases
2025-07-02 17:36:19,374 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /pressreleases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/pressreleases
2025-07-02 17:36:20,380 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.precigen.com/pressreleases
2025-07-02 17:36:50,465 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /pressreleases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/pressreleases
2025-07-02 17:36:50,465 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.precigen.com/pressreleases
2025-07-02 17:36:50,465 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:37:24,681 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/pressreleases, 内容长度: 154082
2025-07-02 17:37:24,730 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/pressreleases, 内容长度: 41755
2025-07-02 17:37:24,731 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:37:30,027 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1124
2025-07-02 17:37:30,027 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:37:30,028 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'press-releases-archive')]//a[@href]",
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='press-release']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'press-releases-archive')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个主要的新闻发布区域，使用了多个<div>和<article>元素来组织新闻条目。每个新闻条目包含一个<a>标签，链接到详细的新闻页面，同时包含日期和标题信息。",
  "sample_links": [
    "https://investors.precigen.com/news-releases/news-release-details/precigen-reports-first-quarter-2025-financial-results-and",
    "https://investors.precigen.com/news-releases/news-release-details/precigen-and-recurrent-respiratory-papillomatosis-foundation-0",
    "https://investors.precigen.com/news-releases/news-release-details/precigen-reports-full-year-2024-financial-results-and-business",
    "https://investors.precigen.com/news-releases/news-release-details/precigen-announce-full-year-2024-financial-results-and-provide",
    "https://investors.precigen.com/news-releases/news-release-details/fda-grants-priority-review-precigens-bla-prgn-2012-treatment"
  ]
}
```
2025-07-02 17:37:30,028 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:37:30,028 - src.core.search_research - INFO - 从 https://investors.precigen.com/pressreleases 提取到 3 个XPath规则
2025-07-02 17:37:31,032 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/events-presentations
2025-07-02 17:37:31,032 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/events-presentations
2025-07-02 17:37:31,032 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.precigen.com/events-presentations
2025-07-02 17:37:37,860 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /events-presentations (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))), URL: https://investors.precigen.com/events-presentations
2025-07-02 17:37:38,871 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.precigen.com/events-presentations
2025-07-02 17:38:08,965 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/events-presentations
2025-07-02 17:38:08,965 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.precigen.com/events-presentations
2025-07-02 17:38:08,965 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.precigen.com/events-presentations
2025-07-02 17:38:43,007 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/events-presentations, 内容长度: 186196
2025-07-02 17:38:43,147 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/events-presentations, 内容长度: 66539
2025-07-02 17:38:43,148 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:38:49,373 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1019
2025-07-02 17:38:49,373 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:38:49,373 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--event--summary')]//a[@href]",
    "//article[contains(@class, 'node--nir-event--nir-widget-list')]//a[@href]",
    "//div[contains(@class, 'nir-widget--event--webcast')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--event--summary')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个事件的列表，每个事件都在一个article元素中，包含日期、标题和链接。新闻链接通常在包含事件摘要的div中，且每个事件都有一个对应的链接。",
  "sample_links": [
    "https://investors.precigen.com/events/event-details/2025-rrp-awareness-day",
    "https://investors.precigen.com/events/event-details/precigen-full-year-2024-financial-results-and-business-updates",
    "https://investors.precigen.com/events/event-details/43rd-annual-jp-morgan-healthcare-conference",
    "https://investors.precigen.com/events/event-details/stifel-2024-healthcare-conference",
    "https://investors.precigen.com/events/event-details/adscc-2nd-bone-marrow-transplant-cellular-therapy-congress-2024"
  ]
}
```
2025-07-02 17:38:49,373 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:38:49,373 - src.core.search_research - INFO - 从 https://investors.precigen.com/events-presentations 提取到 3 个XPath规则
2025-07-02 17:38:50,388 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/stock
2025-07-02 17:38:50,388 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/stock
2025-07-02 17:38:50,388 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.precigen.com/stock
2025-07-02 17:39:20,472 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /stock (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/stock
2025-07-02 17:39:21,485 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.precigen.com/stock
2025-07-02 17:39:51,578 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /stock (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/stock
2025-07-02 17:39:51,578 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.precigen.com/stock
2025-07-02 17:39:51,578 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.precigen.com/stock
2025-07-02 17:40:28,717 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/stock, 内容长度: 160874
2025-07-02 17:40:28,774 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.precigen.com/stock, 内容长度: 46761
2025-07-02 17:40:28,775 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:40:36,168 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 854
2025-07-02 17:40:36,168 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:40:36,169 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='block-content--nir-global-block']//a[contains(@href, '/pressreleases')]",
    "//div[@class='block--system-main-block']//a[contains(@href, '/pressreleases')]",
    "//ul[@id='irNav']//li[contains(@class, 'menu-item_press-events')]//a"
  ],
  "primary_xpath": "//div[@class='block-content--nir-global-block']//a[contains(@href, '/pressreleases')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个区域，其中新闻链接主要集中在'block-content--nir-global-block'和'system-main-block'中，使用了a标签来链接到新闻发布页面。新闻列表的结构可能会因内容更新而变化，但通常保持在这些容器内。",
  "sample_links": [
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ]
}
```
2025-07-02 17:40:36,169 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:40:36,169 - src.core.search_research - INFO - 从 https://investors.precigen.com/stock 提取到 3 个XPath规则
2025-07-02 17:40:37,178 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/financials
2025-07-02 17:40:37,178 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/financials
2025-07-02 17:40:37,178 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.precigen.com/financials
2025-07-02 17:41:07,262 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /financials (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/financials
2025-07-02 17:41:08,272 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.precigen.com/financials
2025-07-02 17:41:38,363 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.precigen.com', port=443): Max retries exceeded with url: /financials (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.precigen.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.precigen.com/financials
2025-07-02 17:41:38,364 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.precigen.com/financials
2025-07-02 17:41:38,364 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.precigen.com/financials
2025-07-02 17:42:18,123 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:42:18,123 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:42:18,197 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:42:18,197 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:42:18,197 - src.core.search_research - INFO - 开始调研公司: WaveBreak
2025-07-02 17:42:18,202 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:42:18,202 - src.services.google_search - INFO - 执行Google搜索: WaveBreak
2025-07-02 17:42:20,928 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:42:20,929 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:42:25,663 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 325
2025-07-02 17:42:25,663 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:42:25,663 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://wavebreak.co/",
  "confidence": 0.90,
  "reasoning": "选择https://wavebreak.co/作为WaveBreak的官方网站，因为该URL直接包含公司名称，并且是一个主流的.com域名。该页面的标题明确指出其为电子商务电子邮件和短信营销机构，符合WaveBreak的业务描述。虽然另一个结果https://wavebreaktx.com/也包含WaveBreak的名称，但它的内容与神经退行性疾病的治疗相关，可能不是该公司的主要业务。综合考虑，wavebreak.co更符合公司的定位和业务。"
}
```
2025-07-02 17:42:25,663 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:42:25,663 - src.core.search_research - INFO - 找到官网: https://wavebreak.co/
2025-07-02 17:42:25,663 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:42:25,663 - src.services.web_scraper - INFO - 开始抓取页面: https://wavebreak.co/
2025-07-02 17:42:25,663 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://wavebreak.co/
2025-07-02 17:42:25,971 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:42:26,973 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://wavebreak.co/
2025-07-02 17:42:27,106 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:42:27,106 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://wavebreak.co/
2025-07-02 17:42:27,552 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:42:27,552 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:42:27,552 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://wavebreak.co/
2025-07-02 17:42:28,551 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 17:43:05,551 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://wavebreak.co/, 内容长度: 206321
2025-07-02 17:43:05,665 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://wavebreak.co/, 内容长度: 109325
2025-07-02 17:43:05,666 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:43:10,706 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 240
2025-07-02 17:43:10,706 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:43:10,706 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在分析提供的HTML内容时，没有找到包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有发现与'财务信息'、'年报'、'公告'等相关词汇的链接。所有链接均未涉及投资者关系相关内容，因此返回空数组。"
}
```
2025-07-02 17:43:10,706 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:43:10,707 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:43:10,707 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:43:11,718 - src.services.web_scraper - INFO - 开始抓取页面: https://wavebreak.co/
2025-07-02 17:43:11,718 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://wavebreak.co/
2025-07-02 17:43:11,857 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:43:12,872 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://wavebreak.co/
2025-07-02 17:43:12,995 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:43:12,995 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://wavebreak.co/
2025-07-02 17:43:12,995 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://wavebreak.co/
2025-07-02 17:43:46,558 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://wavebreak.co/, 内容长度: 205150
2025-07-02 17:43:46,672 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://wavebreak.co/, 内容长度: 109091
2025-07-02 17:43:46,673 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:43:51,277 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 227
2025-07-02 17:43:51,277 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:43:51,277 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在分析过程中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有发现与'财务信息'、'年报'、'公告'等相关词汇的链接。所有链接均未涉及投资者关系相关内容。"
}
```
2025-07-02 17:43:51,277 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:43:51,278 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:43:51,278 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:43:53,280 - src.services.web_scraper - INFO - 开始抓取页面: https://wavebreak.co/
2025-07-02 17:43:53,280 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://wavebreak.co/
2025-07-02 17:43:53,404 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:43:54,419 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://wavebreak.co/
2025-07-02 17:43:54,550 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://wavebreak.co/
2025-07-02 17:43:54,550 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://wavebreak.co/
2025-07-02 17:43:54,550 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://wavebreak.co/
2025-07-02 17:44:10,951 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:44:10,951 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:44:11,022 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:44:11,022 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:44:11,022 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-02 17:44:11,026 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:44:11,026 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-02 17:44:13,783 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:44:13,785 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:44:16,489 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 284
2025-07-02 17:44:16,489 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:44:16,490 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL直接包含了公司名称'Trisalus Life Sciences'，并且是一个主流的.com域名。该网站的内容与公司业务相关，且没有显示为子页面，符合选择官方网站的标准。其他搜索结果中的URL大多为投资者关系或新闻发布页面，未能满足首页的要求。"
}
```
2025-07-02 17:44:16,490 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:44:16,490 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-02 17:44:16,490 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:44:16,490 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-02 17:44:16,490 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-02 17:44:17,255 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 17:44:18,269 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-02 17:44:18,399 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 17:44:18,399 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-02 17:44:18,818 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 17:44:18,818 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 17:44:18,818 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-02 17:44:19,792 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 17:44:53,766 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 277001
2025-07-02 17:44:53,943 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 197781
2025-07-02 17:44:53,943 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:44:58,478 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 995
2025-07-02 17:44:58,478 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:44:58,478 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts",
    "https://investors.trisaluslifesci.com/investor-resources/investor-contacts"
  ],
  "found_keywords": ["投资者关系", "投资者", "Financials", "Press Releases", "Annual Reports"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，识别了多个与投资者关系相关的链接，包括投资者概述、新闻与事件、治理、财务信息等。所有链接均为绝对链接，且没有重复项。关键词的识别涵盖了中英文表述，确保了全面性。"
}
```
2025-07-02 17:44:58,478 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:44:58,478 - src.core.search_research - INFO - 找到投资者关系页面: 10个
2025-07-02 17:44:58,479 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:44:58,479 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:44:58,479 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:44:58,479 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:45:29,673 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:45:30,687 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:46:00,769 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:46:00,769 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:46:00,769 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 17:46:36,271 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188692
2025-07-02 17:46:36,334 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 46195
2025-07-02 17:46:36,334 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:46:40,250 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 796
2025-07-02 17:46:40,250 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:46:40,250 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
    "//section[contains(@class, 'nir-widget--content')]//a[@href]",
    "//div[@class='nir-widget--list']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻链接主要位于包含类名为 'block--nir-news__widget' 的 div 中，链接元素为 a 标签，包含 href 属性。该结构在页面中较为稳定，且包含新闻标题和日期信息，适合提取新闻链接。",
  "sample_links": [
    "/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-02 17:46:40,250 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:46:40,251 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到 3 个XPath规则
2025-07-02 17:46:41,253 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:46:41,253 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:46:41,253 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:47:11,334 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:47:12,337 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:47:42,439 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:47:42,439 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:47:42,439 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 17:48:15,968 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 194151
2025-07-02 17:48:16,034 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 52314
2025-07-02 17:48:16,035 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:48:20,950 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1170
2025-07-02 17:48:20,950 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:48:20,950 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[@href]",
    "//div[@id='lfg-content']//a[contains(@href, '/news-releases/news-release-details')]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面的新闻列表位于包含类名为'nir-widget--list'的div中，每个新闻项以article标签表示，包含新闻标题和链接的a标签。每个新闻项还包含日期和摘要信息，确保了信息的完整性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-220-million-private-placement"
  ]
}
```
2025-07-02 17:48:20,950 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:48:20,950 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到 3 个XPath规则
2025-07-02 17:48:21,953 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:48:21,953 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:48:21,953 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:48:52,070 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:48:53,082 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:49:23,161 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:49:23,163 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:49:23,163 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 17:49:56,336 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 180921
2025-07-02 17:49:56,395 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 41465
2025-07-02 17:49:56,395 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:50:00,986 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 742
2025-07-02 17:50:00,986 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:50:00,986 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-event')]//a[@href]",
    "//div[contains(@class, 'nir-widget--content')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个区域，其中新闻和事件信息主要集中在具有特定类名的div和article元素中。新闻链接通常在这些元素内的a标签中，且包含href属性。日期信息通常在同一层级的其他div中，确保了链接的上下文完整性。",
  "sample_links": [
    "https://edge.media-server.com/mmc/p/vqrjxogk",
    "https://edge.media-server.com/mmc/p/g6oxnay5",
    "https://edge.media-server.com/mmc/p/w7fp8yvn",
    "https://edge.media-server.com/mmc/p/2hmvqta7/",
    "https://edge.media-server.com/mmc/p/hvbgap5o"
  ]
}
```
2025-07-02 17:50:00,986 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:50:00,986 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/events-presentations 提取到 3 个XPath规则
2025-07-02 17:50:01,991 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:01,991 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:01,991 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:08,956 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ProtocolError('Connection aborted.', RemoteDisconnected('Remote end closed connection without response'))), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:09,965 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:40,058 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:40,058 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:50:40,058 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 17:51:13,017 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-02 17:51:13,062 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 32468
2025-07-02 17:51:13,062 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:51:16,693 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 760
2025-07-02 17:51:16,693 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:51:16,693 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-asset')]//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个区域，其中新闻链接主要位于包含类名为'nir-widget--list'的div中，链接元素为a标签，href属性指向新闻的具体链接。每个新闻项以article标签包裹，确保了结构的清晰性和一致性。",
  "sample_links": [
    "/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
    "/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
    "/static-files/ba51dca6-79f3-430c-a3de-a2508a311b02",
    "/static-files/77c4b799-285d-4ee1-b749-130e66699e3b",
    "/static-files/f4ae83c7-039a-49e3-84c0-9a7701085d6b"
  ]
}
```
2025-07-02 17:51:16,694 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:51:16,694 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到 3 个XPath规则
2025-07-02 17:51:17,707 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:51:17,707 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:51:17,707 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:51:47,805 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:51:48,809 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:52:18,902 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:52:18,902 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:52:18,902 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 17:52:51,761 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-02 17:52:51,832 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 58455
2025-07-02 17:52:51,833 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:52:56,730 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 743
2025-07-02 17:52:56,731 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:52:56,731 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='view-content']//tr//td[2]//a[@href]",
    "//table[contains(@class, 'views-table')]//tr//td[2]//a",
    "//div[@class='views-element-container']//a[contains(@href, '/sec-filings/')]"
  ],
  "primary_xpath": "//div[@class='view-content']//tr//td[2]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个表格结构，新闻链接位于表格的第二列的a标签中。每一行代表一个新闻条目，包含日期、表单类型和描述信息。通过分析表格的结构，可以准确提取到所有新闻链接。",
  "sample_links": [
    "/sec-filings/sec-filing/4/0001826667-25-000079",
    "/sec-filings/sec-filing/4/0001826667-25-000083",
    "/sec-filings/sec-filing/sc-i/0001641172-25-016156",
    "/sec-filings/sec-filing/8-k/0001826667-25-000077",
    "/sec-filings/sec-filing/s-4/0001641172-25-016155"
  ]
}
```
2025-07-02 17:52:56,731 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:52:56,732 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到 3 个XPath规则
2025-07-02 17:52:57,735 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:52:57,735 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:52:57,735 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:53:27,817 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:53:28,821 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:53:58,909 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:53:58,909 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:53:58,909 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 17:54:31,876 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 170413
2025-07-02 17:54:31,945 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 32940
2025-07-02 17:54:31,945 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:54:36,950 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1209
2025-07-02 17:54:36,950 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:54:36,950 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'views-field-field-nir-bundle-content')]//a[@href]",
    "//div[@class='view-content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个新闻发布的区域，主要在class为'view-grouping'的div中，每个新闻项包含一个链接，链接的href属性指向具体的新闻发布页面。新闻项的结构通常包括标题和可能的日期信息，确保XPath能够准确选择到这些链接。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-q4-and-full-year-2024-financial",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q3-2024-financial-results-and-provides-business",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q2-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q1-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q4-and-full-year-2023-financial-results-and"
  ]
}
```
2025-07-02 17:54:36,950 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:54:36,950 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/quarterly-results 提取到 3 个XPath规则
2025-07-02 17:54:37,961 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:54:37,962 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:54:37,962 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:55:08,055 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:55:09,061 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:55:39,148 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:55:39,148 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:55:39,148 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 17:56:11,872 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-02 17:56:11,909 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 25122
2025-07-02 17:56:11,910 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:56:15,939 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 583
2025-07-02 17:56:15,940 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:56:15,940 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='nir-widget--list']//a[@href]",
    "//div[@class='nir-widget']//a[contains(@href, '/news-events/')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]"
  ],
  "primary_xpath": "//div[@class='nir-widget--list']//a[@href]",
  "confidence": 0.9,
  "structure_analysis": "页面中包含新闻链接的区域主要在一个具有类名'nir-widget--list'的div中，链接元素为a标签，包含href属性。该结构相对稳定，且在页面中唯一，适合用于提取新闻链接。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 17:56:15,940 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:56:15,940 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到 3 个XPath规则
2025-07-02 17:56:16,953 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:56:16,953 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:56:16,953 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:56:47,044 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:56:48,050 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:57:18,142 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:57:18,142 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:57:18,142 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 17:57:57,624 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 192293
2025-07-02 17:57:57,702 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 53836
2025-07-02 17:57:57,702 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:58:01,655 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 655
2025-07-02 17:58:01,655 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:58:01,655 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-events/')]",
    "//div[@class='block--system-main-block']//a[contains(@href, '/news-events/')]",
    "//nav[@id='block-secondarynavigation-2']//a[contains(@href, '/news-events/')]"
  ],
  "primary_xpath": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-events/')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个导航菜单和内容区域，其中新闻链接主要集中在二级导航菜单中，使用ul/li结构。新闻链接通常是a标签，包含href属性指向新闻相关页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 17:58:01,655 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:58:01,655 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到 3 个XPath规则
2025-07-02 17:58:02,661 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:58:02,661 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:58:02,661 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:58:32,746 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:58:33,761 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:59:03,853 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:59:03,853 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:59:03,853 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 17:59:38,245 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-02 17:59:38,289 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 34050
2025-07-02 17:59:38,289 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:59:42,117 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 665
2025-07-02 17:59:42,117 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:59:42,118 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//ul[contains(@class, 'navbar-nav')]//a[contains(@href, '/news-events/press-releases')]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
  "confidence": 0.80,
  "structure_analysis": "页面包含多个导航和内容区域，新闻链接主要集中在包含新闻和事件的下拉菜单中。主要的新闻链接位于包含新闻列表的div中，且通常以a标签形式存在，href属性指向具体的新闻页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 17:59:42,118 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:59:42,118 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到 3 个XPath规则
2025-07-02 17:59:43,130 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 17:59:43,130 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 17:59:43,130 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:00:13,205 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:00:14,209 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:00:44,286 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=30)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:00:44,286 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:00:44,286 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:01:17,930 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 167996
2025-07-02 18:01:17,972 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 31601
2025-07-02 18:01:17,973 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:01:23,011 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 792
2025-07-02 18:01:23,011 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:01:23,011 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
    "//li[contains(@class, 'dropdown')]//a[contains(@href, '/news-events/press-releases')]",
    "//li[contains(@class, 'dropdown')]//a[contains(@href, '/news-events/events-presentations')]"
  ],
  "primary_xpath": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含新闻链接的区域主要在二级导航菜单中，使用ul/li结构，链接元素为a标签，href属性指向新闻相关页面。新闻链接的选择器基于类名和href属性进行定位，确保能够准确获取到新闻相关的链接。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-02 18:01:23,013 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:01:23,013 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/investor-contacts 提取到 3 个XPath规则
2025-07-02 18:01:24,029 - src.core.search_research - INFO - 总共提取到 26 个唯一XPath规则
2025-07-02 18:01:24,029 - src.core.search_research - INFO - 提取到XPath规则: 26个
2025-07-02 18:01:24,029 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-02 18:01:25,066 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-02 18:01:25,066 - src.services.web_scraper - INFO - DrissionPage资源已清理
