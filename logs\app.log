2025-07-02 18:10:32,447 - __main__ - INFO - AI Tools Find News - 公司调研工具启动
2025-07-02 18:10:32,447 - __main__ - INFO - 准备调研 1 个公司
2025-07-02 18:10:32,447 - __main__ - INFO - 开始调研第 1/1 个公司: TriSalus
2025-07-02 18:10:32,448 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 18:10:32,448 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 18:10:32,515 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 18:10:32,516 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 18:10:32,516 - src.core.search_research - INFO - 开始调研公司: TriSalus
2025-07-02 18:10:32,519 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 18:10:32,529 - src.services.google_search - INFO - 执行Google搜索: TriSalus
2025-07-02 18:10:35,054 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 18:10:35,056 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:10:37,945 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 288
2025-07-02 18:10:37,945 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:10:37,945 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://trisaluslifesci.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://trisaluslifesci.com/作为TriSalus的官方网站，因为该URL包含公司名称，并且是一个主流的.com域名。该页面的标题为'Trisalus Life Sciences'，与公司名称完全一致，且该URL没有指向任何子页面，而是直接指向公司的主页。此外，其他搜索结果均为子页面或投资者关系页面，缺乏作为官方网站的特征。"
}
```
2025-07-02 18:10:37,945 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:10:37,945 - src.core.search_research - INFO - 找到官网: https://trisaluslifesci.com/
2025-07-02 18:10:37,945 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 18:10:37,945 - src.services.web_scraper - INFO - 开始抓取页面: https://trisaluslifesci.com/
2025-07-02 18:10:37,946 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://trisaluslifesci.com/
2025-07-02 18:10:38,076 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 18:10:39,079 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://trisaluslifesci.com/
2025-07-02 18:10:39,203 - src.services.web_scraper - WARNING - requests页面抓取失败，状态码: 403, URL: https://trisaluslifesci.com/
2025-07-02 18:10:39,203 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://trisaluslifesci.com/
2025-07-02 18:10:39,622 - src.services.drission_scraper - INFO - DrissionPage抓取服务初始化完成
2025-07-02 18:10:39,622 - src.services.web_scraper - INFO - DrissionPage抓取服务已初始化
2025-07-02 18:10:39,622 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://trisaluslifesci.com/
2025-07-02 18:10:40,579 - src.services.drission_scraper - INFO - ChromiumPage实例创建成功
2025-07-02 18:10:48,213 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 275975
2025-07-02 18:10:48,387 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://trisaluslifesci.com/, 内容长度: 197692
2025-07-02 18:10:48,388 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:10:52,390 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 994
2025-07-02 18:10:52,390 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:10:52,390 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.trisaluslifesci.com/investor-relations",
    "https://investors.trisaluslifesci.com/news-events/press-releases",
    "https://investors.trisaluslifesci.com/news-events/events-presentations",
    "https://investors.trisaluslifesci.com/governance/governance-overview",
    "https://investors.trisaluslifesci.com/financials/sec-filings",
    "https://investors.trisaluslifesci.com/financials/quarterly-results",
    "https://investors.trisaluslifesci.com/financials/annual-reports",
    "https://investors.trisaluslifesci.com/stock-information",
    "https://investors.trisaluslifesci.com/investor-resources/email-alerts",
    "https://investors.trisaluslifesci.com/investor-resources/investor-contacts"
  ],
  "found_keywords": ["投资者关系", "投资者", "Financials", "SEC Filings", "Annual Reports"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，识别了多个与投资者关系相关的链接，主要集中在导航菜单中。所有链接均为绝对链接，且没有重复。关键词包括'投资者关系'、'投资者'、'财务信息'等，显示出较高的置信度。"
}
```
2025-07-02 18:10:52,390 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 找到投资者关系页面: 10个
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 18:10:52,390 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:52,390 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:52,390 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:57,542 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:10:58,222 - src.services.drission_scraper - WARNING - 关闭DrissionPage浏览器实例时出现警告: 
超时，可能是浏览器卡了。
详情: timeout
方法: Browser.close
参数: {'_timeout': 30}
版本: ********
2025-07-02 18:10:58,222 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 18:10:58,542 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-relations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:03,637 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-relations
2025-07-02 18:11:14,691 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 188291
2025-07-02 18:11:14,753 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-relations, 内容长度: 45954
2025-07-02 18:11:14,754 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:11:19,457 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 800
2025-07-02 18:11:19,457 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:11:19,457 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
    "//div[contains(@class, 'nir-widget--news--headline')]//a",
    "//section[contains(@class, 'block--news-grid')]//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-news__widget')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面中新闻列表位于class为'block--nir-news__widget'的div中，包含多个新闻项，每个新闻项的标题和链接都在a标签内。每个新闻项的日期信息在p标签中，确保了新闻的时间戳存在。",
  "sample_links": [
    "/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer"
  ]
}
```
2025-07-02 18:11:19,457 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:11:19,457 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-relations 提取到 3 个XPath规则
2025-07-02 18:11:20,464 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:20,464 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:20,464 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:25,560 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:26,562 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:28,428 - src.services.drission_scraper - WARNING - 关闭DrissionPage浏览器实例时出现警告: 
超时，可能是浏览器卡了。
详情: timeout
方法: Browser.close
参数: {'_timeout': 30}
版本: ********
2025-07-02 18:11:31,641 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/press-releases (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:31,641 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:31,641 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/press-releases
2025-07-02 18:11:39,728 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 193750
2025-07-02 18:11:39,794 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/press-releases, 内容长度: 52073
2025-07-02 18:11:39,795 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:11:45,748 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1172
2025-07-02 18:11:45,748 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:11:45,748 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-news--nir-widget-list')]//a[@href]",
    "//div[@id='lfg-content']//a[contains(@href, '/news-releases/')]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个主要的新闻列表区域，使用了多个article元素来展示每条新闻。每个article元素中包含一个a标签，链接到具体的新闻发布页面。新闻的标题和链接都在这些a标签中，且每个新闻条目都包含日期信息，确保了信息的完整性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-commencement-exchange-offer-0",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-launch-trinav-flx-infusion",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-announces-chief-financial-officer",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-first-quarter-2025-results-and",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-to-host-first-quarter-2025-financial-results"
  ]
}
```
2025-07-02 18:11:45,748 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:11:45,748 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/press-releases 提取到 3 个XPath规则
2025-07-02 18:11:46,761 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:46,761 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:46,761 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:51,854 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:52,861 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /news-events/events-presentations (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:11:57,947 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/news-events/events-presentations
2025-07-02 18:12:06,774 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 180921
2025-07-02 18:12:06,832 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/news-events/events-presentations, 内容长度: 41465
2025-07-02 18:12:06,832 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:12:11,583 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 866
2025-07-02 18:12:11,583 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:12:11,583 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='nir-widget--list']//article//a[@href]",
    "//div[@class='nir-widget--content']//article//a[@href]",
    "//div[@class='nir-widget--list']//div[contains(@class, 'field-nir-event-title')]//a[@href]",
    "//div[@class='nir-widget--list']//div[contains(@class, 'field-nir-document')]//a[@href]"
  ],
  "primary_xpath": "//div[@class='nir-widget--list']//article//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个新闻和事件的列表，主要在具有类名'nir-widget--list'的div中。每个新闻项以article标签表示，链接在a标签中，包含href属性。日期信息通常在同一article内的其他div中。",
  "sample_links": [
    "https://edge.media-server.com/mmc/p/vqrjxogk",
    "https://edge.media-server.com/mmc/p/g6oxnay5",
    "https://edge.media-server.com/mmc/p/w7fp8yvn",
    "https://edge.media-server.com/mmc/p/2hmvqta7/",
    "https://edge.media-server.com/mmc/p/hvbgap5o"
  ]
}
```
2025-07-02 18:12:11,583 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:12:11,583 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/news-events/events-presentations 提取到 4 个XPath规则
2025-07-02 18:12:12,596 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:12,596 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:12,596 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:17,692 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:18,699 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /governance/governance-overview (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:23,794 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/governance/governance-overview
2025-07-02 18:12:31,679 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 168988
2025-07-02 18:12:31,750 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/governance/governance-overview, 内容长度: 32468
2025-07-02 18:12:31,751 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:12:35,633 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 765
2025-07-02 18:12:35,633 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:12:35,633 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'nir-widget--list')]//a[@href]",
    "//article[contains(@class, 'node--nir-asset')]//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'nir-widget--list')]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含多个新闻和公告的部分，主要在具有类名 'nir-widget--list' 的 div 中。每个新闻项以 article 标签表示，包含一个链接 a 标签，指向具体的新闻内容。该结构相对稳定，适合提取新闻链接。",
  "sample_links": [
    "/static-files/62569002-5a5a-4aac-a4d4-59932b437b9a",
    "/static-files/43e281ed-94d0-435e-90b7-ec78a928def7",
    "/static-files/77c4b799-285d-4ee1-b749-130e66699e3b",
    "/static-files/f4ae83c7-039a-49e3-84c0-9a7701085d6b",
    "/static-files/6268f9b7-fcf4-431a-b603-cf37bedffbe7"
  ]
}
```
2025-07-02 18:12:35,634 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:12:35,634 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/governance/governance-overview 提取到 3 个XPath规则
2025-07-02 18:12:36,647 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:36,648 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:36,648 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:41,750 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:42,756 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,852 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/sec-filings (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,852 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:47,853 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/sec-filings
2025-07-02 18:12:55,719 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 201005
2025-07-02 18:12:55,798 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/sec-filings, 内容长度: 58455
2025-07-02 18:12:55,799 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:00,060 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 738
2025-07-02 18:13:00,061 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:00,061 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='view-content']//tr//td[2]//a[@href]",
    "//table[contains(@class, 'views-table')]//tr//td[2]//a",
    "//div[@class='views-element-container']//a[contains(@href, '/sec-filings/')]"
  ],
  "primary_xpath": "//div[@class='view-content']//tr//td[2]//a[@href]",
  "confidence": 0.90,
  "structure_analysis": "页面包含一个表格结构，新闻链接位于表格的第二列的a标签中。每个链接对应一个SEC文件的详细信息，且每个tr元素代表一条记录，包含日期、表单类型和描述。",
  "sample_links": [
    "/sec-filings/sec-filing/4/0001826667-25-000079",
    "/sec-filings/sec-filing/4/0001826667-25-000083",
    "/sec-filings/sec-filing/sc-i/0001641172-25-016156",
    "/sec-filings/sec-filing/8-k/0001826667-25-000077",
    "/sec-filings/sec-filing/s-4/0001641172-25-016155"
  ]
}
```
2025-07-02 18:13:00,061 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:00,061 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/sec-filings 提取到 3 个XPath规则
2025-07-02 18:13:01,068 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:01,068 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:01,068 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:06,158 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:07,167 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,267 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/quarterly-results (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,268 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:12,268 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/quarterly-results
2025-07-02 18:13:20,193 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 169317
2025-07-02 18:13:20,262 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/quarterly-results, 内容长度: 32939
2025-07-02 18:13:20,263 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:26,025 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 1210
2025-07-02 18:13:26,025 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:26,026 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
    "//div[contains(@class, 'views-field-field-nir-bundle-content')]//a[@href]",
    "//div[@class='view-content']//a[@href]"
  ],
  "primary_xpath": "//div[contains(@class, 'view-grouping')]//a[contains(@href, '/news-releases/')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个新闻发布的区域，主要在class为'view-grouping'的div中。每个新闻项的链接都在a标签内，且href属性包含'/news-releases/'，确保链接指向新闻发布。结构较为稳定，适合使用XPath进行提取。",
  "sample_links": [
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-life-sciences-reports-q4-and-full-year-2024-financial",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q3-2024-financial-results-and-provides-business",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q2-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q1-2024-financial-results-and-business-update",
    "https://trisaluslifesci.com/news-releases/news-release-details/trisalus-reports-q4-and-full-year-2023-financial-results-and"
  ]
}
```
2025-07-02 18:13:26,026 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:26,026 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/quarterly-results 提取到 3 个XPath规则
2025-07-02 18:13:27,037 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:27,037 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:27,037 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:32,148 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:33,151 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /financials/annual-reports (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:38,242 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/financials/annual-reports
2025-07-02 18:13:45,953 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 160080
2025-07-02 18:13:45,991 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/financials/annual-reports, 内容长度: 25122
2025-07-02 18:13:45,991 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:13:50,296 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 530
2025-07-02 18:13:50,296 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:13:50,296 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[@class='nir-widget--list']//a[@href]",
    "//div[@class='nir-widget--content']//a[@href]",
    "//section//a[contains(@href, '/news-events/')]"
  ],
  "primary_xpath": "//div[@class='nir-widget--list']//a[@href]",
  "confidence": 0.9,
  "structure_analysis": "页面中没有明确的新闻列表结构，新闻链接可能嵌套在多个div中，主要在类名为'nir-widget--list'的div中寻找链接。该结构可能会因内容更新而变化，因此需要定期验证XPath的有效性。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 18:13:50,297 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:13:50,297 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/financials/annual-reports 提取到 3 个XPath规则
2025-07-02 18:13:51,301 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:51,301 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:51,301 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:56,390 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:13:57,404 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /stock-information (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:02,483 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/stock-information
2025-07-02 18:14:14,784 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 192293
2025-07-02 18:14:14,871 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/stock-information, 内容长度: 53836
2025-07-02 18:14:14,872 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:14:18,682 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 766
2025-07-02 18:14:18,682 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:14:18,682 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-events')]",
    "//div[@class='block--nir-stock-chart']//a[contains(@href, '/news-events')]",
    "//nav[@id='block-secondarynavigation-2']//a[contains(@href, '/news-events')]"
  ],
  "primary_xpath": "//ul[@class='navbar-nav navbar-secondary-links']//a[contains(@href, '/news-events')]",
  "confidence": 0.90,
  "structure_analysis": "页面包含多个导航菜单和下拉列表，其中新闻链接主要集中在二级导航的新闻与事件部分。通过分析结构，发现新闻链接通常在包含新闻相关的ul或nav元素中，且链接的href属性包含'/news-events'。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/news-and-events/press-releases",
    "https://trisaluslifesci.com/news-and-events/events-presentations"
  ]
}
```
2025-07-02 18:14:18,683 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:14:18,683 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/stock-information 提取到 3 个XPath规则
2025-07-02 18:14:19,692 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:19,692 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:19,692 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:24,776 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:25,790 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/email-alerts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:30,890 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/email-alerts
2025-07-02 18:14:39,750 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 171319
2025-07-02 18:14:39,800 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/email-alerts, 内容长度: 34050
2025-07-02 18:14:39,801 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:14:43,755 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 740
2025-07-02 18:14:43,755 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:14:43,755 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events')]",
    "//ul[contains(@class, 'navbar-nav')]//a[contains(@href, '/news-events/press-releases')]"
  ],
  "primary_xpath": "//div[contains(@class, 'block--nir-email-alerts-signup-block')]//a[@href]",
  "confidence": 0.80,
  "structure_analysis": "页面中包含多个导航和内容区域，新闻链接主要集中在包含新闻和事件的下拉菜单中，以及特定的新闻发布区域。主要的新闻链接位于包含新闻列表的div中，且通常以a标签形式存在，href属性指向具体的新闻页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/press-media/"
  ]
}
```
2025-07-02 18:14:43,755 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:14:43,755 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/email-alerts 提取到 3 个XPath规则
2025-07-02 18:14:44,758 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:44,758 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:44,758 - src.services.web_scraper - INFO - 使用requests进行第 1 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:49,858 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:50,860 - src.services.web_scraper - INFO - 使用requests进行第 2 次尝试: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.web_scraper - WARNING - requests页面抓取请求异常: HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Max retries exceeded with url: /investor-resources/investor-contacts (Caused by ReadTimeoutError("HTTPSConnectionPool(host='investors.trisaluslifesci.com', port=443): Read timed out. (read timeout=5)")), URL: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.web_scraper - INFO - requests重试失败，尝试使用DrissionPage作为备用方案: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:14:55,959 - src.services.drission_scraper - INFO - 使用DrissionPage开始抓取页面: https://investors.trisaluslifesci.com/investor-resources/investor-contacts
2025-07-02 18:15:04,332 - src.services.drission_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 167996
2025-07-02 18:15:04,384 - src.services.web_scraper - INFO - DrissionPage页面抓取成功: https://investors.trisaluslifesci.com/investor-resources/investor-contacts, 内容长度: 31601
2025-07-02 18:15:04,385 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 18:15:07,628 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 822
2025-07-02 18:15:07,628 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 18:15:07,628 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "xpath_rules": [
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
    "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/press-media/')]",
    "//a[contains(@href, '/news-events/press-releases')]",
    "//a[contains(@href, '/news-events/events-presentations')]"
  ],
  "primary_xpath": "//ul[contains(@class, 'navbar-secondary-links')]//a[contains(@href, '/news-and-events/')]",
  "confidence": 0.90,
  "structure_analysis": "页面中包含新闻链接的主要区域是二级导航栏，使用ul/li结构，链接元素为a标签，href属性指向新闻相关页面。",
  "sample_links": [
    "https://trisaluslifesci.com/news-and-events/",
    "https://trisaluslifesci.com/press-media/",
    "https://trisaluslifesci.com/news-events/press-releases",
    "https://trisaluslifesci.com/news-events/events-presentations"
  ]
}
```
2025-07-02 18:15:07,629 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 18:15:07,629 - src.core.search_research - INFO - 从 https://investors.trisaluslifesci.com/investor-resources/investor-contacts 提取到 4 个XPath规则
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 总共提取到 30 个唯一XPath规则
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 提取到XPath规则: 30个
2025-07-02 18:15:08,637 - src.core.search_research - INFO - 公司调研完成: TriSalus
2025-07-02 18:15:09,673 - src.services.drission_scraper - INFO - DrissionPage浏览器实例已关闭
2025-07-02 18:15:09,673 - src.services.web_scraper - INFO - DrissionPage资源已清理
2025-07-02 18:15:09,673 - __main__ - INFO - ✓ TriSalus 调研完成
2025-07-02 18:15:09,674 - __main__ - INFO - 调研结果已保存到: research_results.json
2025-07-02 18:15:09,674 - __main__ - INFO - 所有公司调研完成
