2025-07-02 17:01:12,596 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:01:12,596 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:01:12,640 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:01:12,640 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:01:19,278 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: company
2025-07-02 17:01:37,144 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: company
2025-07-02 17:03:13,407 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:03:13,407 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:03:13,583 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:03:13,583 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:03:13,583 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:03:13,587 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:03:13,587 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:17,332 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:17,334 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:17,334 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:17,334 - src.utils.retry_decorator - WARNING - 函数 _search_official_website 第 1 次尝试失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   , 1.0秒后重试
2025-07-02 17:03:18,337 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:21,022 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:21,023 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:21,023 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:21,023 - src.utils.retry_decorator - WARNING - 函数 _search_official_website 第 2 次尝试失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   , 2.0秒后重试
2025-07-02 17:03:23,029 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:03:25,818 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:03:25,820 - src.utils.prompt_manager - ERROR - 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.core.search_research - ERROR - 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.utils.retry_decorator - ERROR - 函数 _search_official_website 重试 2 次后仍然失败: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:03:25,820 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 搜索官网失败: 变量替换失败: 缺少必要的变量: 
     "official_website": "https://example.com",
     "confidence": 0.95,
     "reasoning": "选择理由的详细说明"
   
2025-07-02 17:05:03,769 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:05:03,769 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:05:03,816 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:05:03,816 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:05:03,816 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:05:03,818 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:05:03,818 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:05:06,385 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:05:06,386 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:09,904 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 210
2025-07-02 17:05:09,905 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:09,905 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:05:09,905 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:05:09,905 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:11,065 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:11,066 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:14,291 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 226
2025-07-02 17:05:14,291 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:14,291 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:14,291 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:05:15,304 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:16,255 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:16,256 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:19,675 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 244
2025-07-02 17:05:19,676 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:19,676 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:19,676 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:05:21,686 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:05:22,643 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:05:22,644 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:05:31,470 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 222
2025-07-02 17:05:31,470 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:05:31,470 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:31,470 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:05:31,470 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:07,892 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:06:07,892 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:06:07,938 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:06:07,938 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:06:07,938 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:06:07,940 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:06:07,940 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:06:10,710 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:06:10,711 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:13,220 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 244
2025-07-02 17:06:13,220 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:13,220 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:06:13,220 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:06:13,220 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:14,160 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:14,162 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:16,655 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:06:16,655 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:16,655 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:16,655 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:06:17,664 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:18,602 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:18,603 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:20,541 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:06:20,541 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:20,541 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:20,542 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:06:22,553 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:23,516 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:23,517 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:25,985 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:06:25,985 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:25,985 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:25,985 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:25,985 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:48,849 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:06:48,849 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:06:48,896 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:06:48,896 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:06:48,896 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:06:48,898 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:06:48,898 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:06:51,338 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:06:51,339 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:54,048 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 320
2025-07-02 17:06:54,049 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:54,049 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性。其他结果虽然也与Precigen相关，但均为子页面或投资者页面，不符合首页的要求。"
}
```
2025-07-02 17:06:54,049 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:06:54,049 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:06:54,049 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:06:54,049 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:06:54,998 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:06:54,999 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:06:58,497 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 246
2025-07-02 17:06:58,497 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:06:58,497 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有发现与'财务信息'、'年报'、'公告'等相关词汇的链接。由于HTML内容似乎是损坏或乱码，无法进行有效的链接提取和分析。"
}
```
2025-07-02 17:06:58,497 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:06:58,497 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:06:58,497 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:06:59,507 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:00,466 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:07:00,467 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:02,637 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 238
2025-07-02 17:07:02,637 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:02,637 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能识别出任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有找到与'财务信息'、'年报'、'公告'等相关词汇的链接。由于内容为乱码，无法进行有效的链接提取和分析。"
}
```
2025-07-02 17:07:02,638 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:02,638 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:02,638 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:07:04,646 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:05,620 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12041
2025-07-02 17:07:05,621 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:07,335 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:07:07,335 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:07,335 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:07:07,335 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:07,335 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:07,335 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:07,336 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:33,602 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:07:33,602 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:07:33,644 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:07:33,644 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:07:33,644 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:07:33,646 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:07:33,647 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:07:36,162 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:07:36,164 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:38,467 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 264
2025-07-02 17:07:38,467 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:38,467 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且该域名为.com，符合主流域名标准。此外，该URL没有包含任何子页面，直接指向公司的首页，且内容描述与公司业务高度相关，显示出其作为官方网站的权威性。"
}
```
2025-07-02 17:07:38,467 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:38,467 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:07:38,467 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:07:38,467 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:39,428 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:39,428 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:41,546 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 222
2025-07-02 17:07:41,546 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:41,546 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:07:41,547 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:41,547 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:41,547 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:07:42,559 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:43,527 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:43,528 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:45,886 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:07:45,886 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:45,886 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。由于内容被损坏或无法解析，无法提取有效的链接信息。"
}
```
2025-07-02 17:07:45,886 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:45,886 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:45,886 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:07:47,892 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:07:48,851 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:07:48,852 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:07:51,225 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:07:51,225 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:07:51,225 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:07:51,225 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:07:51,225 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:51,227 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:07:51,227 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:08:13,209 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:08:13,210 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:08:14,179 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:08:55,586 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:08:55,586 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:08:56,518 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:09:25,224 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:09:25,224 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:09:26,141 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:10:12,403 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:10:12,403 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:10:12,454 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:10:12,454 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:10:12,454 - src.core.search_research - INFO - 开始调研公司: 苹果公司
2025-07-02 17:10:12,456 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:10:12,456 - src.services.google_search - INFO - 执行Google搜索: 苹果公司
2025-07-02 17:10:15,151 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:10:15,152 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:18,290 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 304
2025-07-02 17:10:18,290 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:18,290 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://www.apple.com/",
  "confidence": 0.98,
  "reasoning": "选择https://www.apple.com/作为苹果公司的官方网站，因为该URL直接对应于苹果公司的主域名，且是以.com结尾的主流域名。该页面提供了苹果公司的所有产品和服务信息，符合官方网站的标准。虽然https://www.apple.com.cn/也是一个有效的官方网站，但它是针对中国大陆用户的子域名，优先选择了全球主站。其他结果如维基百科和百度百科等均为第三方平台，不符合官方网站的标准。"
}
```
2025-07-02 17:10:18,290 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:18,290 - src.core.search_research - INFO - 找到官网: https://www.apple.com/
2025-07-02 17:10:18,290 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:10:18,290 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:18,475 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:18,475 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:21,459 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 247
2025-07-02 17:10:21,459 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:21,459 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中搜索了包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，以及'财务信息'、'年报'、'公告'等相关词汇的链接。所有链接均未包含相关内容。"
}
```
2025-07-02 17:10:21,459 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:21,459 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:21,459 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:10:22,470 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:22,565 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:22,565 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:25,004 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 255
2025-07-02 17:10:25,004 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:25,004 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中检查了导航菜单、页脚和主要内容区域，但未发现包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有与'财务信息'、'年报'、'公告'等相关词汇的链接。"
}
```
2025-07-02 17:10:25,004 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:25,004 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:25,004 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:10:27,015 - src.services.web_scraper - INFO - 开始抓取页面: https://www.apple.com/
2025-07-02 17:10:27,116 - src.services.web_scraper - INFO - 页面抓取成功: https://www.apple.com/, 内容长度: 103291
2025-07-02 17:10:27,117 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:10:29,321 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 254
2025-07-02 17:10:29,322 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:10:29,322 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何与投资者关系相关的链接或关键词。分析过程中检查了导航菜单、页脚和主要内容区域，未发现包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接，也没有与'财务信息'、'年报'、'公告'等相关词汇的链接。"
}
```
2025-07-02 17:10:29,322 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:10:29,322 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:29,322 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:10:29,322 - src.core.search_research - ERROR - 公司调研失败: 苹果公司, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:04,523 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:11:04,524 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:11:04,599 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:11:04,599 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:11:04,599 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:11:04,604 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:11:04,604 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:11:07,214 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:11:07,217 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:14,750 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 313
2025-07-02 17:11:14,750 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:14,750 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com域名，符合官方网站的标准。该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性和相关性。其他结果虽然也包含Precigen的相关信息，但均为子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:11:14,750 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:14,751 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:11:14,751 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:11:14,751 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:15,670 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:15,671 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:18,019 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 223
2025-07-02 17:11:18,020 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:18,020 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:11:18,020 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:18,020 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:18,020 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 1.0秒后重试
2025-07-02 17:11:19,033 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:19,330 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:19,331 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:21,584 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:11:21,584 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:21,584 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，没有找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'、'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法提取有效的链接信息。"
}
```
2025-07-02 17:11:21,584 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:21,584 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:21,584 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 2 次尝试失败: 投资者关系页面分析置信度过低: 0.0, 2.0秒后重试
2025-07-02 17:11:23,596 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:11:23,894 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 12984
2025-07-02 17:11:23,894 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:11:25,815 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 225
2025-07-02 17:11:25,815 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:11:25,815 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [],
  "found_keywords": [],
  "confidence": 0.0,
  "analysis_notes": "在提供的HTML内容中，未能找到任何包含'投资者关系'、'投资者'、'IR'、'Investor Relations'、'财务信息'、'年报'或'公告'等相关关键词的链接。内容似乎是乱码或损坏，无法进行有效的链接提取。"
}
```
2025-07-02 17:11:25,815 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:11:25,815 - src.core.search_research - WARNING - 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:25,815 - src.utils.retry_decorator - ERROR - 函数 _extract_investor_relations_urls 重试 2 次后仍然失败: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:11:25,815 - src.core.search_research - ERROR - 公司调研失败: Precigen, 错误: 投资者关系页面分析置信度过低: 0.0
2025-07-02 17:12:44,755 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:12:44,756 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:12:44,831 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:12:44,831 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:12:44,831 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:12:44,835 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:12:44,835 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:12:47,340 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:12:47,342 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:12:51,283 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 208
2025-07-02 17:12:51,284 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:12:51,284 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "该URL是Precigen的官方网站首页，标题明确标识为'Precigen: Home'，且域名为主流的.com后缀，符合官方网站的标准。其他结果虽然包含公司名称，但都是子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:12:51,284 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:12:51,284 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:12:51,284 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:12:51,285 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:13:15,852 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:13:15,853 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:13:15,930 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:13:15,930 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:13:15,930 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:13:15,934 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:13:15,934 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:13:19,148 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:13:19,150 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:13:21,451 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 297
2025-07-02 17:13:21,451 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:13:21,451 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com后缀，符合官方网站的标准。此外，该页面的内容描述与公司业务高度相关，进一步确认了其作为官方网站的权威性。其他结果虽然也包含Precigen的相关信息，但均为子页面或投资者相关页面，不符合首页的要求。"
}
```
2025-07-02 17:13:21,452 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:13:21,452 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:13:21,452 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:13:21,452 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:13:22,372 - src.services.web_scraper - ERROR - 页面抓取过程中发生未知错误: local variable 'content' referenced before assignment, URL: https://precigen.com/
2025-07-02 17:13:22,373 - src.core.search_research - WARNING - 无法获取首页内容: https://precigen.com/
2025-07-02 17:13:22,373 - src.utils.retry_decorator - WARNING - 函数 _extract_investor_relations_urls 第 1 次尝试失败: 无法获取首页内容: https://precigen.com/, 1.0秒后重试
2025-07-02 17:13:34,785 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:13:34,785 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:13:34,860 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:13:34,860 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:13:34,861 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:13:34,864 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:13:34,864 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:13:37,387 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:13:37,388 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:13:40,544 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 316
2025-07-02 17:13:40,544 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:13:40,545 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心信息，进一步确认了其作为官方网站的权威性。其他结果虽然也包含precigen.com的子页面，但优先选择首页以符合要求。"
}
```
2025-07-02 17:13:40,545 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:13:40,545 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:13:40,545 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:13:40,546 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:15:53,242 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:15:53,242 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:15:53,311 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:15:53,312 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:15:53,312 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:15:53,315 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:15:53,315 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:15:55,971 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:15:55,973 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:15:58,879 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 256
2025-07-02 17:15:58,879 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:15:58,880 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com后缀，符合公司名称的官方域名标准。此外，该链接是首页而非子页面，且没有涉及第三方平台，具有较高的权威性和相关性。"
}
```
2025-07-02 17:15:58,880 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:15:58,880 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:15:58,880 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:15:58,881 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:16:48,345 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:16:48,345 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:16:48,422 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:16:48,422 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:16:48,422 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:16:48,426 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:16:48,426 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:16:50,912 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:16:50,913 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:16:54,343 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 318
2025-07-02 17:16:54,344 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:16:54,344 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为precigen.com，符合公司名称并且是主流的.com域名。此外，该页面的内容描述了Precigen作为一家生物制药公司的核心业务，进一步确认了其作为官方网站的权威性。其他结果虽然也包含precigen.com的子页面，但优先选择首页以满足要求。"
}
```
2025-07-02 17:16:54,344 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:16:54,344 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:16:54,344 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:16:54,345 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:16:57,687 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 35557
2025-07-02 17:16:58,380 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:17:02,089 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 590
2025-07-02 17:17:02,089 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:17:02,089 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.precigen.com/",
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/stock",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ],
  "found_keywords": ["投资者", "Investor Relations"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，首先检查了HTML内容中的导航菜单和页脚部分，发现了多个与投资者关系相关的链接。所有链接均包含'投资者'或相关财务信息的关键词，且均为绝对链接。经过去重处理，最终列出了7个独特的投资者关系链接。"
}
```
2025-07-02 17:17:02,089 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:17:02,089 - src.core.search_research - INFO - 找到投资者关系页面: 7个
2025-07-02 17:17:02,090 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:17:02,090 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/
2025-07-02 17:17:02,090 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/
2025-07-02 17:17:32,839 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/
2025-07-02 17:18:03,930 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 3/3, URL: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.services.web_scraper - ERROR - 页面抓取最终超时: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - WARNING - 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - WARNING - 分析投资者关系页面失败: https://investors.precigen.com/, 错误: 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:18:36,033 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/pressreleases
2025-07-02 17:18:36,033 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:19:06,118 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/pressreleases
2025-07-02 17:19:37,214 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/pressreleases
2025-07-02 17:19:59,592 - src.services.google_search - INFO - Google搜索服务初始化完成
2025-07-02 17:19:59,593 - src.services.web_scraper - INFO - 网页抓取服务初始化完成
2025-07-02 17:19:59,673 - src.services.ai_analyzer - INFO - AI分析服务初始化完成
2025-07-02 17:19:59,674 - src.core.search_research - INFO - SearchResearchClass 初始化完成
2025-07-02 17:19:59,674 - src.core.search_research - INFO - 开始调研公司: Precigen
2025-07-02 17:19:59,678 - src.core.search_research - INFO - 步骤1: 搜索公司官网
2025-07-02 17:19:59,678 - src.services.google_search - INFO - 执行Google搜索: Precigen
2025-07-02 17:20:02,276 - src.services.google_search - INFO - 搜索成功，返回 10 个结果
2025-07-02 17:20:02,277 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:20:04,914 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 256
2025-07-02 17:20:04,914 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:20:04,914 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "official_website": "https://precigen.com/",
  "confidence": 0.98,
  "reasoning": "选择了https://precigen.com/作为Precigen公司的官方网站首页URL，因为该URL直接对应于搜索结果中标题为'Precigen: Home'的条目，且其域名为主流的.com域名，符合公司名称的官方域名标准。此外，该页面的内容描述与公司业务高度相关，进一步确认了其作为官方网站的权威性。"
}
```
2025-07-02 17:20:04,914 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:20:04,914 - src.core.search_research - INFO - 找到官网: https://precigen.com/
2025-07-02 17:20:04,914 - src.core.search_research - INFO - 步骤2: 分析官网首页
2025-07-02 17:20:04,914 - src.services.web_scraper - INFO - 开始抓取页面: https://precigen.com/
2025-07-02 17:20:05,882 - src.services.web_scraper - INFO - 页面抓取成功: https://precigen.com/, 内容长度: 35557
2025-07-02 17:20:05,883 - src.services.ai_analyzer - INFO - 开始AI分析
2025-07-02 17:20:08,341 - src.services.ai_analyzer - INFO - AI分析完成，结果长度: 630
2025-07-02 17:20:08,341 - src.services.ai_analyzer - ERROR - AI返回结果不是有效的JSON: Expecting value: line 1 column 1 (char 0)
2025-07-02 17:20:08,341 - src.services.ai_analyzer - ERROR - AI返回内容: ```json
{
  "investor_relations_urls": [
    "https://investors.precigen.com/",
    "https://investors.precigen.com/pressreleases",
    "https://investors.precigen.com/events-presentations",
    "https://investors.precigen.com/stock",
    "https://investors.precigen.com/financials",
    "https://investors.precigen.com/governance",
    "https://investors.precigen.com/resources"
  ],
  "found_keywords": ["投资者", "Investor Relations"],
  "confidence": 0.95,
  "analysis_notes": "在分析过程中，我搜索了包含'投资者关系'、'投资者'、'IR'、'Investor Relations'等关键词的链接。所有相关链接均位于导航菜单中的'Investors'部分及其子链接中。所有链接均为绝对链接，且没有重复。置信度为0.95，因所有链接均清晰且直接指向投资者关系相关内容。"
}
```
2025-07-02 17:20:08,341 - src.services.ai_analyzer - INFO - JSON修复成功
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 找到投资者关系页面: 7个
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 步骤3: 分析投资者关系页面
2025-07-02 17:20:08,341 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/
2025-07-02 17:20:08,341 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/
2025-07-02 17:20:38,431 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 1/3, URL: https://investors.precigen.com/
2025-07-02 17:21:09,525 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 3/3, URL: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.services.web_scraper - ERROR - 页面抓取最终超时: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.core.search_research - WARNING - 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:21:41,643 - src.core.search_research - WARNING - 分析投资者关系页面失败: https://investors.precigen.com/, 错误: 无法获取页面内容: https://investors.precigen.com/
2025-07-02 17:21:41,644 - src.core.search_research - INFO - 分析投资者关系页面: https://investors.precigen.com/pressreleases
2025-07-02 17:21:41,644 - src.services.web_scraper - INFO - 开始抓取页面: https://investors.precigen.com/pressreleases
2025-07-02 17:21:48,621 - src.services.web_scraper - ERROR - 页面抓取请求异常: ('Connection aborted.', RemoteDisconnected('Remote end closed connection without response')), URL: https://investors.precigen.com/pressreleases
2025-07-02 17:22:29,230 - src.services.web_scraper - WARNING - 页面抓取超时，尝试 2/3, URL: https://investors.precigen.com/pressreleases
