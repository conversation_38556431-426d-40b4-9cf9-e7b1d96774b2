# AI Tools Find News - 公司调研工具

一个基于AI驱动的公司调研工具，能够自动搜索公司官网、分析投资者关系页面，并提取新闻链接的XPath规则。

## 功能特性

- 🔍 **智能搜索**: 使用Google搜索API自动找到公司官方网站
- 🤖 **AI分析**: 利用大语言模型分析网页结构和内容
- 📊 **结构化输出**: 生成标准化的JSON格式调研结果
- 🔄 **错误重试**: 内置重试机制和完善的错误处理
- 📝 **模块化Prompt**: 支持变量替换的提示词管理系统
- 🚀 **批量处理**: 支持批量调研多个公司

## 项目结构

```
ai_tools_find_news/
├── src/                    # 源代码目录
│   ├── core/              # 核心模块
│   │   └── search_research.py  # 主要调研类
│   ├── services/          # 服务模块
│   │   ├── google_search.py    # Google搜索服务
│   │   ├── web_scraper.py      # 网页抓取服务
│   │   └── ai_analyzer.py      # AI分析服务
│   ├── utils/             # 工具模块
│   │   ├── prompt_manager.py   # Prompt管理器
│   │   ├── validators.py       # 数据验证器
│   │   ├── exceptions.py       # 自定义异常
│   │   ├── retry_decorator.py  # 重试装饰器
│   │   └── logger.py          # 日志工具
│   └── config.py          # 配置管理
├── docs/                  # AI提示词目录
│   ├── search/           # 搜索相关提示词
│   ├── analysis/         # 分析相关提示词
│   └── extraction/       # 提取相关提示词
├── tests/                # 测试用例
├── examples/             # 使用示例
├── main.py              # 主程序入口
├── requirements.txt     # 依赖包列表
└── .env                # 环境配置文件
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

复制并编辑 `.env` 文件：

```bash
# AI API配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-4o-mini
OPENAI_TIMEOUT=60

# Google搜索API配置
GOOGLE_SEARCH_API_URL=your_google_search_api_url

# 网页抓取配置
SCRAPING_TIMEOUT=30
MAX_RETRIES=3
REQUEST_DELAY=1

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
```

## 使用方法

### 命令行使用

```bash
# 调研单个公司
python main.py "苹果公司"

# 调研多个公司
python main.py "苹果公司" "腾讯控股" "阿里巴巴集团"

# 指定输出文件
python main.py "苹果公司" -o my_results.json

# 静默模式
python main.py "苹果公司" --quiet
```

### 编程接口使用

```python
from src.core.search_research import SearchResearchClass

# 创建调研实例
researcher = SearchResearchClass()

# 调研单个公司
result = researcher.research_company("苹果公司")

# 查看结果
print(result)
```

## 输出数据结构

```json
{
  "company_name": "苹果公司",
  "base_url": "https://www.apple.com.cn",
  "investor_relations_urls": [
    "https://investor.apple.com",
    "https://www.apple.com.cn/investor-relations"
  ],
  "news_xpath_rules": [
    "//div[@class='news-list']//a[@href]",
    "//ul[@id='press-releases']//li//a"
  ],
  "research_timestamp": 1703123456.789,
  "status": "completed"
}
```

### 字段说明

- `company_name`: 公司名称
- `base_url`: 公司官方网站首页URL
- `investor_relations_urls`: 投资者关系页面URL列表
- `news_xpath_rules`: 新闻链接的XPath选择器规则列表
- `research_timestamp`: 调研时间戳
- `status`: 调研状态 (`processing`/`completed`/`failed`)

## 调研流程

1. **搜索官网**: 使用Google搜索API搜索公司名称，通过AI分析搜索结果识别官方网站
2. **分析首页**: 抓取官网首页HTML，通过AI分析提取投资者关系页面链接
3. **提取规则**: 分析投资者关系页面结构，通过AI提取新闻链接的XPath选择器规则

## Prompt管理系统

项目使用模块化的Prompt管理系统，支持变量替换：

```python
from src.utils.prompt_manager import PromptManager

pm = PromptManager()
prompt = pm.load_prompt('search/find_official_website.txt', {
    'company_name': '苹果公司',
    'search_results': search_data
})
```

### Prompt文件组织

- `docs/search/`: 搜索相关提示词
- `docs/analysis/`: 分析相关提示词  
- `docs/extraction/`: 提取相关提示词

## 错误处理

项目实现了完善的错误处理机制：

- **自定义异常**: 针对不同类型的错误定义专门的异常类
- **重试机制**: 对网络请求和AI分析实现自动重试
- **日志记录**: 详细的日志记录便于问题排查
- **数据验证**: 输入输出数据的严格验证

## 测试

运行测试用例：

```bash
# 运行所有测试
python -m pytest tests/

# 运行特定测试
python -m pytest tests/test_prompt_manager.py

# 运行单个测试方法
python -m pytest tests/test_validators.py::TestDataValidator::test_validate_url_valid
```

## 示例

查看 `examples/` 目录中的使用示例：

```bash
# 运行基本使用示例
python examples/basic_usage.py
```

## 注意事项

1. **API配置**: 确保正确配置OpenAI API和Google搜索API
2. **请求频率**: 注意控制请求频率，避免触发API限制
3. **网站结构**: 不同网站的结构差异可能影响分析结果
4. **数据质量**: AI分析结果的质量取决于输入数据的质量

## 许可证

本项目采用 MIT 许可证。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 更新日志

### v1.0.0
- 初始版本发布
- 实现基本的公司调研功能
- 支持Google搜索和AI分析
- 完善的错误处理和重试机制
