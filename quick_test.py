"""
快速验证脚本
用于验证系统的基本功能是否正常
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.config import Config
from src.utils.prompt_manager import PromptManager
from src.utils.validators import DataValidator
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def test_config():
    """测试配置"""
    print("1. 测试配置...")
    try:
        Config.validate()
        print("   ✓ 配置验证通过")
        return True
    except Exception as e:
        print(f"   ✗ 配置验证失败: {e}")
        return False

def test_prompt_manager():
    """测试Prompt管理器"""
    print("2. 测试Prompt管理器...")
    try:
        pm = PromptManager()
        prompts = pm.list_prompts()
        
        if prompts:
            print(f"   ✓ 找到 {len(prompts)} 个提示词文件")
            
            # 测试加载第一个提示词
            first_prompt = prompts[0]
            content = pm.load_prompt(first_prompt)
            print(f"   ✓ 成功加载提示词: {first_prompt}")
            return True
        else:
            print("   ✗ 未找到提示词文件")
            return False
            
    except Exception as e:
        print(f"   ✗ Prompt管理器测试失败: {e}")
        return False

def test_validators():
    """测试数据验证器"""
    print("3. 测试数据验证器...")
    try:
        # 测试URL验证
        assert DataValidator.validate_url("https://www.example.com") == True
        assert DataValidator.validate_url("invalid-url") == False
        
        # 测试公司名称验证
        assert DataValidator.validate_company_name("苹果公司") == True
        assert DataValidator.validate_company_name("") == False
        
        # 测试XPath验证
        assert DataValidator.validate_xpath("//div[@class='news']") == True
        assert DataValidator.validate_xpath("invalid-xpath") == False
        
        print("   ✓ 数据验证器测试通过")
        return True
        
    except Exception as e:
        print(f"   ✗ 数据验证器测试失败: {e}")
        return False

def test_services_import():
    """测试服务模块导入"""
    print("4. 测试服务模块导入...")
    try:
        from src.services.google_search import GoogleSearchService
        from src.services.web_scraper import WebScraperService
        from src.services.ai_analyzer import AIAnalyzerService
        
        print("   ✓ 服务模块导入成功")
        return True
        
    except Exception as e:
        print(f"   ✗ 服务模块导入失败: {e}")
        return False

def test_core_import():
    """测试核心模块导入"""
    print("5. 测试核心模块导入...")
    try:
        from src.core.search_research import SearchResearchClass
        
        # 尝试创建实例
        researcher = SearchResearchClass()
        print("   ✓ 核心模块导入和实例化成功")
        return True
        
    except Exception as e:
        print(f"   ✗ 核心模块导入失败: {e}")
        return False

def test_directory_structure():
    """测试目录结构"""
    print("6. 测试目录结构...")
    
    required_dirs = [
        'src',
        'src/core',
        'src/services', 
        'src/utils',
        'docs',
        'docs/search',
        'docs/analysis',
        'docs/extraction',
        'tests',
        'examples'
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"   ✗ 缺少目录: {', '.join(missing_dirs)}")
        return False
    else:
        print("   ✓ 目录结构完整")
        return True

def test_required_files():
    """测试必需文件"""
    print("7. 测试必需文件...")
    
    required_files = [
        'main.py',
        'requirements.txt',
        '.env',
        'README.md',
        'src/config.py',
        'src/core/search_research.py',
        'docs/search/find_official_website.txt',
        'docs/analysis/extract_investor_relations.txt',
        'docs/extraction/extract_news_xpath.txt'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"   ✗ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("   ✓ 必需文件完整")
        return True

def main():
    """主函数"""
    print("AI Tools Find News - 快速验证")
    print("=" * 50)
    
    tests = [
        test_directory_structure,
        test_required_files,
        test_config,
        test_prompt_manager,
        test_validators,
        test_services_import,
        test_core_import
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"   ✗ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"验证结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有验证项目通过！系统准备就绪。")
        print("\n下一步:")
        print("1. 运行示例: python examples/basic_usage.py")
        print("2. 调研公司: python main.py \"公司名称\"")
        print("3. 运行测试: python run_tests.py")
        return True
    else:
        print("❌ 部分验证项目失败，请检查配置和依赖。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
