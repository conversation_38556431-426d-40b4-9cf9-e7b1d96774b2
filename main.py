"""
AI Tools Find News - 主程序入口
公司调研工具的主要执行文件
"""
import json
import sys
import argparse
from typing import List, Dict, Any

from src.config import Config
from src.core.search_research import SearchResearchClass
from src.utils.logger import setup_logger
from src.utils.exceptions import AIToolsException

logger = setup_logger(__name__)

def research_single_company(company_name: str) -> Dict[str, Any]:
    """
    调研单个公司
    
    Args:
        company_name: 公司名称
        
    Returns:
        调研结果
    """
    try:
        # 验证配置
        Config.validate()
        
        # 创建调研实例
        researcher = SearchResearchClass()
        
        # 执行调研
        result = researcher.research_company(company_name)
        
        return result
        
    except AIToolsException as e:
        logger.error(f"调研过程中发生业务异常: {e}")
        return {
            "company_name": company_name,
            "status": "failed",
            "error": str(e)
        }
    except Exception as e:
        logger.error(f"调研过程中发生未知异常: {e}")
        return {
            "company_name": company_name,
            "status": "failed",
            "error": f"未知异常: {str(e)}"
        }

def research_multiple_companies(company_names: List[str]) -> List[Dict[str, Any]]:
    """
    批量调研多个公司
    
    Args:
        company_names: 公司名称列表
        
    Returns:
        调研结果列表
    """
    results = []
    
    for i, company_name in enumerate(company_names, 1):
        logger.info(f"开始调研第 {i}/{len(company_names)} 个公司: {company_name}")
        
        result = research_single_company(company_name)
        results.append(result)
        
        # 打印进度
        status = result.get('status', 'unknown')
        if status == 'completed':
            logger.info(f"✓ {company_name} 调研完成")
        else:
            logger.warning(f"✗ {company_name} 调研失败: {result.get('error', '未知错误')}")
    
    return results

def save_results_to_file(results: List[Dict[str, Any]], filename: str = "research_results.json"):
    """
    保存调研结果到文件
    
    Args:
        results: 调研结果列表
        filename: 输出文件名
    """
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        logger.info(f"调研结果已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存结果文件失败: {e}")

def print_summary(results: List[Dict[str, Any]]):
    """
    打印调研结果摘要
    
    Args:
        results: 调研结果列表
    """
    total = len(results)
    completed = sum(1 for r in results if r.get('status') == 'completed')
    failed = total - completed
    
    print("\n" + "="*50)
    print("调研结果摘要")
    print("="*50)
    print(f"总计: {total} 个公司")
    print(f"成功: {completed} 个")
    print(f"失败: {failed} 个")
    print(f"成功率: {completed/total*100:.1f}%")
    
    if completed > 0:
        print("\n成功调研的公司:")
        for result in results:
            if result.get('status') == 'completed':
                company = result['company_name']
                ir_count = len(result.get('investor_relations_urls', []))
                xpath_count = len(result.get('news_xpath_rules', []))
                print(f"  • {company}: {ir_count}个投资者关系页面, {xpath_count}个XPath规则")
    
    if failed > 0:
        print("\n调研失败的公司:")
        for result in results:
            if result.get('status') != 'completed':
                company = result['company_name']
                error = result.get('error', '未知错误')
                print(f"  • {company}: {error}")

def main1():
    """主函数"""
    parser = argparse.ArgumentParser(description='AI驱动的公司调研工具')
    parser.add_argument('companies', nargs='+', help='要调研的公司名称')
    parser.add_argument('-o', '--output', default='research_results.json', 
                       help='输出文件名 (默认: research_results.json)')
    parser.add_argument('--no-save', action='store_true', 
                       help='不保存结果到文件')
    parser.add_argument('--quiet', action='store_true', 
                       help='静默模式，只输出错误信息')
    
    args = parser.parse_args()
    
    if args.quiet:
        # 设置日志级别为ERROR
        import logging
        logging.getLogger().setLevel(logging.ERROR)
    
    try:
        logger.info("AI Tools Find News - 公司调研工具启动")
        logger.info(f"准备调研 {len(args.companies)} 个公司")
        
        # 执行调研
        results = research_multiple_companies(args.companies)
        
        # 保存结果
        if not args.no_save:
            save_results_to_file(results, args.output)
        
        # 打印摘要
        if not args.quiet:
            print_summary(results)
        
        # 返回适当的退出码
        failed_count = sum(1 for r in results if r.get('status') != 'completed')
        if failed_count == 0:
            logger.info("所有公司调研完成")
            sys.exit(0)
        elif failed_count < len(results):
            logger.warning("部分公司调研失败")
            sys.exit(1)
        else:
            logger.error("所有公司调研失败")
            sys.exit(2)
            
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
        sys.exit(130)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    research_single_company("TriSalus")
