"""
测试DrissionPage备用方案
"""
import sys
import os
import unittest
import time
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.services.web_scraper import WebScraperService
from src.services.drission_scraper import DrissionScraperService
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class TestDrissionFallback(unittest.TestCase):
    """测试DrissionPage备用方案"""
    
    def setUp(self):
        """设置测试环境"""
        self.web_scraper = WebScraperService()
    
    def tearDown(self):
        """清理测试环境"""
        if hasattr(self.web_scraper, '_drission_scraper') and self.web_scraper._drission_scraper:
            self.web_scraper.close()
    
    def test_normal_requests_success(self):
        """测试正常requests请求成功的情况"""
        # 测试一个正常的网站
        test_url = "https://httpbin.org/html"
        
        try:
            content = self.web_scraper.fetch_page(test_url)
            self.assertIsNotNone(content, "正常网站应该能够成功抓取")
            self.assertIn("html", content.lower(), "内容应该包含HTML")
            logger.info("正常requests请求测试通过")
        except Exception as e:
            logger.warning(f"正常requests请求测试失败（可能是网络问题）: {e}")
            self.skipTest("网络连接问题，跳过测试")
    
    @patch('src.services.web_scraper.requests.get')
    def test_requests_failure_drission_success(self, mock_requests_get):
        """测试requests失败后DrissionPage成功的情况"""
        # 模拟requests失败
        mock_requests_get.side_effect = Exception("模拟网络错误")
        
        # 模拟DrissionPage成功
        with patch.object(self.web_scraper, '_get_drission_scraper') as mock_get_drission:
            mock_drission = MagicMock()
            mock_drission.fetch_page.return_value = "<html><body>DrissionPage成功</body></html>"
            mock_get_drission.return_value = mock_drission
            
            test_url = "https://example.com"
            content = self.web_scraper.fetch_page(test_url)
            
            self.assertIsNotNone(content, "DrissionPage应该能够成功抓取")
            self.assertIn("DrissionPage成功", content, "内容应该来自DrissionPage")
            
            # 验证requests被调用了2次（前两次重试）
            self.assertEqual(mock_requests_get.call_count, 2, "requests应该被调用2次")
            
            # 验证DrissionPage被调用了
            mock_drission.fetch_page.assert_called_once_with(test_url)
            
            logger.info("requests失败DrissionPage成功测试通过")
    
    @patch('src.services.web_scraper.requests.get')
    def test_all_methods_failure(self, mock_requests_get):
        """测试所有方法都失败的情况"""
        # 模拟requests失败
        mock_requests_get.side_effect = Exception("模拟网络错误")
        
        # 模拟DrissionPage也失败
        with patch.object(self.web_scraper, '_get_drission_scraper') as mock_get_drission:
            mock_drission = MagicMock()
            mock_drission.fetch_page.return_value = None
            mock_get_drission.return_value = mock_drission
            
            test_url = "https://example.com"
            content = self.web_scraper.fetch_page(test_url)
            
            self.assertIsNone(content, "所有方法失败时应该返回None")
            
            # 验证requests被调用了2次
            self.assertEqual(mock_requests_get.call_count, 2, "requests应该被调用2次")
            
            # 验证DrissionPage被调用了
            mock_drission.fetch_page.assert_called_once_with(test_url)
            
            logger.info("所有方法失败测试通过")
    
    def test_fetch_page_with_js(self):
        """测试专门的JS页面抓取方法"""
        with patch.object(self.web_scraper, '_get_drission_scraper') as mock_get_drission:
            mock_drission = MagicMock()
            mock_drission.fetch_page_with_js_wait.return_value = "<html><body>JS渲染成功</body></html>"
            mock_get_drission.return_value = mock_drission
            
            test_url = "https://spa-example.com"
            wait_selector = ".content"
            wait_time = 3.0
            
            content = self.web_scraper.fetch_page_with_js(test_url, wait_selector, wait_time)
            
            self.assertIsNotNone(content, "JS页面抓取应该成功")
            self.assertIn("JS渲染成功", content, "内容应该来自DrissionPage")
            
            # 验证DrissionPage被正确调用
            mock_drission.fetch_page_with_js_wait.assert_called_once_with(test_url, wait_selector, wait_time)
            
            logger.info("JS页面抓取测试通过")
    
    def test_drission_scraper_initialization(self):
        """测试DrissionPage服务的延迟初始化"""
        # 初始时应该没有DrissionPage实例
        self.assertIsNone(self.web_scraper._drission_scraper)
        
        # 调用获取方法后应该创建实例
        try:
            drission_scraper = self.web_scraper._get_drission_scraper()
            self.assertIsNotNone(drission_scraper)
            self.assertIsInstance(drission_scraper, DrissionScraperService)
            
            # 再次调用应该返回同一个实例
            drission_scraper2 = self.web_scraper._get_drission_scraper()
            self.assertIs(drission_scraper, drission_scraper2)
            
            logger.info("DrissionPage延迟初始化测试通过")
        except Exception as e:
            logger.warning(f"DrissionPage初始化测试失败（可能是环境问题）: {e}")
            self.skipTest("DrissionPage环境问题，跳过测试")
    
    def test_resource_cleanup(self):
        """测试资源清理"""
        # 创建DrissionPage实例
        try:
            drission_scraper = self.web_scraper._get_drission_scraper()
            self.assertIsNotNone(self.web_scraper._drission_scraper)
            
            # 调用清理方法
            self.web_scraper.close()
            self.assertIsNone(self.web_scraper._drission_scraper)
            
            logger.info("资源清理测试通过")
        except Exception as e:
            logger.warning(f"资源清理测试失败（可能是环境问题）: {e}")
            self.skipTest("DrissionPage环境问题，跳过测试")

if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDrissionFallback)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n所有测试通过！")
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
