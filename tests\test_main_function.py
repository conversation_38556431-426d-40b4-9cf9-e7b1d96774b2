"""
测试新的main函数
"""
import sys
import os
import unittest
import tempfile
from unittest.mock import patch, MagicMock

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from main import main, research_single_company, research_multiple_companies
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class TestMainFunction(unittest.TestCase):
    """测试新的main函数"""
    
    def setUp(self):
        """设置测试环境"""
        self.test_companies = ["TestCompany1", "TestCompany2"]
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """清理测试环境"""
        import shutil
        try:
            shutil.rmtree(self.temp_dir)
        except:
            pass
    
    @patch('main.research_multiple_companies')
    def test_main_basic_usage(self, mock_research):
        """测试main函数基本用法"""
        # 模拟调研结果
        mock_results = [
            {
                "company_name": "TestCompany1",
                "status": "completed",
                "base_url": "https://test1.com"
            },
            {
                "company_name": "TestCompany2", 
                "status": "failed",
                "error": "测试错误"
            }
        ]
        mock_research.return_value = mock_results
        
        # 调用main函数
        results = main(
            companies=self.test_companies,
            save_results=False,  # 不保存文件
            print_summary_info=False  # 不打印摘要
        )
        
        # 验证结果
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["company_name"], "TestCompany1")
        self.assertEqual(results[1]["company_name"], "TestCompany2")
        
        # 验证调用
        mock_research.assert_called_once_with(self.test_companies)
        
        logger.info("main函数基本用法测试通过")
    
    @patch('main.research_multiple_companies')
    @patch('main.save_results_to_file')
    def test_main_with_file_save(self, mock_save, mock_research):
        """测试main函数保存文件功能"""
        # 模拟调研结果
        mock_results = [{"company_name": "TestCompany", "status": "completed"}]
        mock_research.return_value = mock_results
        
        output_file = os.path.join(self.temp_dir, "test_results.json")
        
        # 调用main函数
        results = main(
            companies=["TestCompany"],
            output_file=output_file,
            save_results=True,
            print_summary_info=False
        )
        
        # 验证保存文件被调用
        mock_save.assert_called_once_with(mock_results, output_file)
        
        logger.info("main函数文件保存测试通过")
    
    @patch('main.research_multiple_companies')
    @patch('main.print_summary')
    def test_main_quiet_mode(self, mock_print_summary, mock_research):
        """测试main函数静默模式"""
        # 模拟调研结果
        mock_results = [{"company_name": "TestCompany", "status": "completed"}]
        mock_research.return_value = mock_results
        
        # 调用main函数（静默模式）
        results = main(
            companies=["TestCompany"],
            save_results=False,
            quiet_mode=True,
            print_summary_info=True  # 即使设置为True，静默模式下也不应该打印
        )
        
        # 验证摘要没有被打印
        mock_print_summary.assert_not_called()
        
        logger.info("main函数静默模式测试通过")
    
    @patch('main.research_multiple_companies')
    @patch('main.print_summary')
    def test_main_print_summary(self, mock_print_summary, mock_research):
        """测试main函数打印摘要功能"""
        # 模拟调研结果
        mock_results = [{"company_name": "TestCompany", "status": "completed"}]
        mock_research.return_value = mock_results
        
        # 调用main函数（非静默模式，打印摘要）
        results = main(
            companies=["TestCompany"],
            save_results=False,
            quiet_mode=False,
            print_summary_info=True
        )
        
        # 验证摘要被打印
        mock_print_summary.assert_called_once_with(mock_results)
        
        logger.info("main函数打印摘要测试通过")
    
    def test_main_empty_companies_list(self):
        """测试main函数空公司列表"""
        # 调用main函数（空列表）
        results = main(
            companies=[],
            save_results=False,
            print_summary_info=False
        )
        
        # 验证返回空结果
        self.assertEqual(len(results), 0)
        
        logger.info("main函数空列表测试通过")
    
    @patch('main.research_multiple_companies')
    def test_main_exception_handling(self, mock_research):
        """测试main函数异常处理"""
        # 模拟调研异常
        mock_research.side_effect = Exception("测试异常")
        
        # 调用main函数应该抛出异常
        with self.assertRaises(Exception) as context:
            main(
                companies=["TestCompany"],
                save_results=False,
                print_summary_info=False
            )
        
        self.assertIn("测试异常", str(context.exception))
        
        logger.info("main函数异常处理测试通过")
    
    def test_main_default_parameters(self):
        """测试main函数默认参数"""
        with patch('main.research_multiple_companies') as mock_research:
            with patch('main.save_results_to_file') as mock_save:
                with patch('main.print_summary') as mock_print:
                    # 模拟调研结果
                    mock_results = [{"company_name": "TestCompany", "status": "completed"}]
                    mock_research.return_value = mock_results
                    
                    # 调用main函数（只传入必需参数）
                    results = main(["TestCompany"])
                    
                    # 验证默认行为
                    mock_save.assert_called_once()  # 默认保存文件
                    mock_print.assert_called_once()  # 默认打印摘要
        
        logger.info("main函数默认参数测试通过")
    
    def test_research_single_company_success(self):
        """测试单个公司调研成功"""
        with patch('main.Config.validate'), \
             patch('main.SearchResearchClass') as mock_class:

            # 模拟SearchResearchClass实例
            mock_instance = MagicMock()
            mock_class.return_value = mock_instance

            # 模拟成功结果
            mock_result = {
                "company_name": "TestCompany",
                "status": "completed",
                "base_url": "https://test.com"
            }
            mock_instance.research_company.return_value = mock_result

            # 直接调用research_single_company
            result = research_single_company("TestCompany")

            # 验证结果
            self.assertEqual(result["company_name"], "TestCompany")
            self.assertEqual(result["status"], "completed")

            logger.info("单个公司调研成功测试通过")
    
    @patch('main.research_single_company')
    def test_research_multiple_companies(self, mock_research_single):
        """测试多个公司调研"""
        # 模拟单个公司调研结果
        def mock_single_research(company_name):
            return {
                "company_name": company_name,
                "status": "completed" if company_name == "TestCompany1" else "failed",
                "error": None if company_name == "TestCompany1" else "测试错误"
            }
        
        mock_research_single.side_effect = mock_single_research
        
        # 调用多公司调研
        results = research_multiple_companies(["TestCompany1", "TestCompany2"])
        
        # 验证结果
        self.assertEqual(len(results), 2)
        self.assertEqual(results[0]["status"], "completed")
        self.assertEqual(results[1]["status"], "failed")
        
        logger.info("多个公司调研测试通过")

if __name__ == '__main__':
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMainFunction)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果
    if result.wasSuccessful():
        print("\n所有测试通过！")
    else:
        print(f"\n测试失败: {len(result.failures)} 个失败, {len(result.errors)} 个错误")
