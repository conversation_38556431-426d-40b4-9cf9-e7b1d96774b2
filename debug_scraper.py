"""
调试网页抓取
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.web_scraper import WebScraperService

def debug_scraper():
    """调试网页抓取"""
    import requests

    url = "https://precigen.com/"
    print(f"直接抓取URL: {url}")

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1'
    }

    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"编码: {response.encoding}")
        print(f"Content-Type: {response.headers.get('content-type')}")

        if response.status_code == 200:
            content = response.text
            print(f"内容长度: {len(content)}")
            print("前500个字符:")
            print("-" * 50)
            print(content[:500])
            print("-" * 50)

            # 保存到文件
            with open("debug_content_direct.html", "w", encoding="utf-8") as f:
                f.write(content)
            print("完整内容已保存到 debug_content_direct.html")
        else:
            print(f"请求失败: {response.status_code}")
    except Exception as e:
        print(f"请求异常: {e}")

    print("\n" + "="*50)
    print("使用WebScraperService:")

    scraper = WebScraperService()
    content = scraper.fetch_page(url)

    if content:
        print(f"内容长度: {len(content)}")
        print("前500个字符:")
        print("-" * 50)
        print(content[:500])
    else:
        print("抓取失败")

if __name__ == "__main__":
    debug_scraper()
