# AI Tools Find News - 项目开发总结

## 项目概述

AI Tools Find News 是一个基于AI驱动的公司调研工具，能够自动化执行以下任务：

1. **搜索公司官网** - 使用Google搜索API找到公司官方网站
2. **分析网页结构** - 通过AI分析HTML内容提取投资者关系页面
3. **提取XPath规则** - 生成新闻链接的XPath选择器规则
4. **输出结构化数据** - 生成标准化的JSON格式调研结果

## 技术架构

### 核心组件

1. **SearchResearchClass** (`src/core/search_research.py`)
   - 主要的调研协调器
   - 实现完整的调研流程
   - 集成所有服务组件

2. **服务层** (`src/services/`)
   - `GoogleSearchService` - Google搜索API集成
   - `WebScraperService` - 网页抓取服务
   - `AIAnalyzerService` - AI分析服务

3. **工具层** (`src/utils/`)
   - `PromptManager` - AI提示词管理
   - `DataValidator` - 数据验证
   - `RetryDecorator` - 重试机制
   - `Logger` - 日志记录

### 设计特点

- **模块化设计** - 各组件职责清晰，易于维护和扩展
- **错误处理** - 完善的异常处理和重试机制
- **数据验证** - 严格的输入输出数据验证
- **配置管理** - 集中的环境配置管理
- **日志记录** - 详细的操作日志便于调试

## 实现的功能

### ✅ 已完成功能

1. **项目基础结构**
   - 完整的目录结构
   - 配置管理系统
   - 日志记录系统

2. **Prompt管理系统**
   - 支持变量替换的提示词管理
   - 模块化的提示词组织
   - 动态加载和缓存机制

3. **核心搜索调研类**
   - 完整的调研流程实现
   - 结构化的JSON输出
   - 错误处理和状态管理

4. **Google搜索集成**
   - Google搜索API集成
   - 搜索结果解析和标准化
   - 重试机制和错误处理

5. **网页分析功能**
   - HTTP请求和网页抓取
   - HTML内容清理和处理
   - 链接提取和URL标准化

6. **XPath提取功能**
   - AI驱动的XPath规则生成
   - 多页面分析和规则合并
   - 置信度评估和过滤

7. **错误处理和重试机制**
   - 自定义异常类型
   - 指数退避重试策略
   - 完善的日志记录

8. **测试和示例**
   - 单元测试覆盖核心功能
   - 使用示例和文档
   - 快速验证脚本

## 技术实现细节

### AI提示词系统

- **文件组织**: 按功能模块分类存储
- **变量替换**: 支持花括号语法 `{variable_name}`
- **缓存机制**: 避免重复读取文件
- **错误处理**: 缺少变量时的友好提示

### 数据流程

```
输入公司名称 → Google搜索 → AI分析搜索结果 → 提取官网URL
     ↓
获取官网HTML → AI分析页面结构 → 提取投资者关系页面URL
     ↓
访问投资者页面 → AI分析页面结构 → 提取新闻XPath规则
     ↓
输出结构化JSON结果
```

### 错误处理策略

- **分层异常**: 不同类型的错误使用专门的异常类
- **重试机制**: 网络请求和AI分析支持自动重试
- **降级处理**: 部分失败时继续处理其他部分
- **详细日志**: 记录所有关键操作和错误信息

## 输出数据格式

```json
{
  "company_name": "公司名称",
  "base_url": "https://company.com",
  "investor_relations_urls": [
    "https://company.com/investors"
  ],
  "news_xpath_rules": [
    "//div[@class='news-list']//a[@href]"
  ],
  "research_timestamp": 1703123456.789,
  "status": "completed"
}
```

## 使用方式

### 命令行使用
```bash
python main.py "苹果公司" "腾讯控股"
```

### 编程接口
```python
from src.core.search_research import SearchResearchClass

researcher = SearchResearchClass()
result = researcher.research_company("苹果公司")
```

## 测试覆盖

- **单元测试**: 16个测试用例，100%通过
- **功能测试**: 覆盖核心组件的主要功能
- **集成测试**: 验证组件间的协作
- **快速验证**: 自动化的系统健康检查

## 配置要求

### 必需配置
- `OPENAI_API_KEY` - OpenAI API密钥
- `OPENAI_BASE_URL` - OpenAI API端点
- `GOOGLE_SEARCH_API_URL` - Google搜索API端点

### 可选配置
- 网页抓取超时设置
- 重试次数和延迟配置
- 日志级别和输出设置

## 性能特点

- **并发处理**: 支持批量处理多个公司
- **缓存机制**: 提示词文件缓存减少IO
- **请求控制**: 内置延迟避免API限制
- **内存优化**: 大HTML内容截断处理

## 扩展性

### 易于扩展的部分
- **新的AI提示词**: 添加新的分析场景
- **新的数据源**: 集成其他搜索API
- **新的输出格式**: 支持不同的数据格式
- **新的验证规则**: 增加数据质量检查

### 架构优势
- **松耦合设计**: 各组件独立，易于替换
- **配置驱动**: 行为可通过配置调整
- **插件化**: 支持功能模块的动态加载

## 项目质量

- **代码规范**: 遵循Python PEP8规范
- **文档完整**: 详细的代码注释和使用文档
- **测试覆盖**: 核心功能的单元测试
- **错误处理**: 完善的异常处理机制

## 部署建议

1. **环境准备**: Python 3.8+，安装依赖包
2. **配置设置**: 正确配置API密钥和端点
3. **权限检查**: 确保网络访问权限
4. **日志目录**: 创建日志输出目录
5. **测试验证**: 运行快速验证脚本

## 总结

AI Tools Find News 项目成功实现了AI驱动的公司调研自动化，具有以下特点：

- ✅ **功能完整**: 实现了从搜索到分析的完整流程
- ✅ **架构清晰**: 模块化设计，职责分离
- ✅ **质量保证**: 完善的测试和错误处理
- ✅ **易于使用**: 简单的命令行和编程接口
- ✅ **可扩展性**: 支持功能扩展和定制

项目已准备好投入使用，可以有效提高公司调研的效率和准确性。
