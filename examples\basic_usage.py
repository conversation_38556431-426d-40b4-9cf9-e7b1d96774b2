"""
基本使用示例
演示如何使用AI Tools Find News进行公司调研
"""
import sys
import os
import json

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.core.search_research import SearchResearchClass
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

def basic_research_example():
    """基本调研示例"""
    print("=== AI Tools Find News - 基本使用示例 ===\n")
    
    try:
        # 验证配置
        Config.validate()
        print("✓ 配置验证通过")
        
        # 创建调研实例
        researcher = SearchResearchClass()
        print("✓ 调研实例创建成功")
        
        # 要调研的公司
        company_name = "苹果公司"
        print(f"\n开始调研公司: {company_name}")
        
        # 执行调研
        result = researcher.research_company(company_name)
        
        # 显示结果
        print("\n调研结果:")
        print("=" * 50)
        print(f"公司名称: {result['company_name']}")
        print(f"状态: {result['status']}")
        
        if result['status'] == 'completed':
            print(f"官网URL: {result['base_url']}")
            print(f"投资者关系页面数量: {len(result['investor_relations_urls'])}")
            print(f"XPath规则数量: {len(result['news_xpath_rules'])}")
            
            if result['investor_relations_urls']:
                print("\n投资者关系页面:")
                for i, url in enumerate(result['investor_relations_urls'], 1):
                    print(f"  {i}. {url}")
            
            if result['news_xpath_rules']:
                print("\nXPath规则:")
                for i, xpath in enumerate(result['news_xpath_rules'], 1):
                    print(f"  {i}. {xpath}")
        else:
            print(f"调研失败: {result.get('error', '未知错误')}")
        
        # 保存结果到文件
        output_file = "example_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"示例执行失败: {e}")
        logger.error(f"示例执行失败: {e}")

def batch_research_example():
    """批量调研示例"""
    print("\n=== 批量调研示例 ===\n")
    
    try:
        # 要调研的公司列表
        companies = [
            "腾讯控股有限公司",
            "阿里巴巴集团",
            "百度公司"
        ]
        
        researcher = SearchResearchClass()
        results = []
        
        for i, company in enumerate(companies, 1):
            print(f"调研进度: {i}/{len(companies)} - {company}")
            
            result = researcher.research_company(company)
            results.append(result)
            
            status = "✓" if result['status'] == 'completed' else "✗"
            print(f"  {status} {result['status']}")
        
        # 统计结果
        completed = sum(1 for r in results if r['status'] == 'completed')
        print(f"\n批量调研完成:")
        print(f"  总计: {len(companies)} 个公司")
        print(f"  成功: {completed} 个")
        print(f"  失败: {len(companies) - completed} 个")
        
        # 保存批量结果
        output_file = "batch_results.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"  结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"批量调研示例失败: {e}")
        logger.error(f"批量调研示例失败: {e}")

def prompt_management_example():
    """Prompt管理示例"""
    print("\n=== Prompt管理示例 ===\n")
    
    try:
        from src.utils.prompt_manager import PromptManager
        
        pm = PromptManager()
        
        # 列出所有可用的提示词文件
        prompts = pm.list_prompts()
        print("可用的提示词文件:")
        for prompt in prompts:
            print(f"  • {prompt}")
        
        # 加载并使用提示词
        if 'search/find_official_website.txt' in prompts:
            print("\n加载搜索提示词示例:")
            prompt_content = pm.load_prompt(
                'search/find_official_website.txt',
                {
                    'company_name': '示例公司',
                    'search_results': '[{"title": "示例", "url": "https://example.com"}]'
                }
            )
            print("提示词内容预览:")
            print(prompt_content[:200] + "..." if len(prompt_content) > 200 else prompt_content)
        
    except Exception as e:
        print(f"Prompt管理示例失败: {e}")
        logger.error(f"Prompt管理示例失败: {e}")

def main():
    """主函数"""
    print("AI Tools Find News - 使用示例")
    print("=" * 50)
    
    # 检查环境配置
    try:
        Config.validate()
        print("✓ 环境配置检查通过\n")
    except Exception as e:
        print(f"✗ 环境配置检查失败: {e}")
        print("请检查.env文件中的配置项")
        return
    
    # 运行示例
    try:
        # 基本使用示例
        basic_research_example()
        
        # Prompt管理示例
        prompt_management_example()
        
        # 询问是否运行批量示例
        response = input("\n是否运行批量调研示例? (y/N): ").strip().lower()
        if response in ['y', 'yes']:
            batch_research_example()
        
        print("\n示例运行完成!")
        
    except KeyboardInterrupt:
        print("\n用户中断示例执行")
    except Exception as e:
        print(f"\n示例执行过程中发生错误: {e}")
        logger.error(f"示例执行错误: {e}")

if __name__ == "__main__":
    main()
