"""
测试Prompt管理器
"""
import unittest
import tempfile
import os
from src.utils.prompt_manager import PromptManager

class TestPromptManager(unittest.TestCase):
    """Prompt管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.pm = PromptManager(base_path=self.temp_dir)
        
        # 创建测试提示词文件
        test_prompt_dir = os.path.join(self.temp_dir, 'test')
        os.makedirs(test_prompt_dir, exist_ok=True)
        
        test_prompt_content = "Hello {name}, welcome to {company}!"
        test_prompt_path = os.path.join(test_prompt_dir, 'greeting.txt')
        
        with open(test_prompt_path, 'w', encoding='utf-8') as f:
            f.write(test_prompt_content)
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir)
    
    def test_load_prompt_with_variables(self):
        """测试加载提示词并替换变量"""
        result = self.pm.load_prompt('test/greeting.txt', {
            'name': '张三',
            'company': '测试公司'
        })
        
        expected = "Hello 张三, welcome to 测试公司!"
        self.assertEqual(result, expected)
    
    def test_load_prompt_without_variables(self):
        """测试加载提示词不替换变量"""
        result = self.pm.load_prompt('test/greeting.txt')
        expected = "Hello {name}, welcome to {company}!"
        self.assertEqual(result, expected)
    
    def test_missing_variables(self):
        """测试缺少变量时的异常"""
        with self.assertRaises(ValueError):
            self.pm.load_prompt('test/greeting.txt', {'name': '张三'})
    
    def test_file_not_found(self):
        """测试文件不存在时的异常"""
        with self.assertRaises(FileNotFoundError):
            self.pm.load_prompt('nonexistent/file.txt')
    
    def test_list_prompts(self):
        """测试列出提示词文件"""
        prompts = self.pm.list_prompts()
        self.assertIn('test/greeting.txt', prompts)

if __name__ == '__main__':
    unittest.main()
