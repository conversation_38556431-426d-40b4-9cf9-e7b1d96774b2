"""
网页抓取服务
"""
import requests
import time
from typing import Optional
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from src.config import Config
from src.utils.logger import setup_logger

# 禁用SSL警告
import urllib3
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

logger = setup_logger(__name__)

class WebScraperService:
    """网页抓取服务类"""

    def __init__(self):
        """初始化网页抓取服务"""
        self.timeout = Config.SCRAPING_TIMEOUT
        self.max_retries = Config.MAX_RETRIES
        self.request_delay = Config.REQUEST_DELAY

        # 设置请求头，模拟真实浏览器
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }

        # # 创建session以支持自动解压缩
        # self.session = requests.Session()
        # self.session.headers.update(self.headers)

        # 延迟初始化DrissionPage服务（避免启动时就创建浏览器实例）
        self._drission_scraper = None

        logger.info("网页抓取服务初始化完成")

    def _get_drission_scraper(self):
        """
        获取DrissionPage抓取服务实例（延迟初始化）

        Returns:
            DrissionScraperService实例
        """
        if self._drission_scraper is None:
            try:
                from src.services.drission_scraper import DrissionScraperService
                self._drission_scraper = DrissionScraperService()
                logger.info("DrissionPage抓取服务已初始化")
            except Exception as e:
                logger.error(f"初始化DrissionPage抓取服务失败: {e}")
                raise
        return self._drission_scraper
    
    def fetch_page(self, url: str, encoding: str = 'utf-8') -> Optional[str]:
        """
        抓取网页内容
        使用三层重试策略：
        1. 第一次和第二次使用requests
        2. 第三次使用DrissionPage作为备用方案

        Args:
            url: 目标URL
            encoding: 页面编码

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"开始抓取页面: {url}")

        # 前两次使用requests重试
        requests_max_retries = min(2, self.max_retries)

        for attempt in range(requests_max_retries):
            try:
                logger.info(f"使用requests进行第 {attempt + 1} 次尝试: {url}")

                # 发送请求，使用session自动处理压缩
                response = requests.get(
                    url,
                    timeout=self.timeout,
                    headers=self.headers,
                    allow_redirects=True,
                    verify=False  # 暂时禁用SSL验证以处理某些网站的SSL问题
                )

                # 检查响应状态
                if response.status_code == 200:
                    # 尝试自动检测编码
                    response.encoding = 'utf-8'
                    content = response.text
                    # 清理HTML内容
                    content = self.clean_html_content(content)

                    logger.info(f"requests页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content

                elif response.status_code in [301, 302, 303, 307, 308]:
                    # 处理重定向
                    redirect_url = response.headers.get('Location')
                    if redirect_url:
                        logger.info(f"页面重定向: {url} -> {redirect_url}")
                        return self.fetch_page(redirect_url, encoding)

                else:
                    logger.warning(f"requests页面抓取失败，状态码: {response.status_code}, URL: {url}")
                    if attempt < requests_max_retries - 1:
                        time.sleep(self.request_delay * (attempt + 1))
                        continue

            except requests.exceptions.Timeout:
                logger.warning(f"requests页面抓取超时，尝试 {attempt + 1}/{requests_max_retries}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except requests.exceptions.RequestException as e:
                logger.warning(f"requests页面抓取请求异常: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

            except Exception as e:
                logger.warning(f"requests页面抓取过程中发生未知错误: {e}, URL: {url}")
                if attempt < requests_max_retries - 1:
                    time.sleep(self.request_delay * (attempt + 1))
                    continue

        # 如果requests重试失败，且配置允许更多重试，则使用DrissionPage
        if self.max_retries > requests_max_retries:
            logger.info(f"requests重试失败，尝试使用DrissionPage作为备用方案: {url}")
            try:
                drission_scraper = self._get_drission_scraper()
                content = drission_scraper.fetch_page(url)

                if content:
                    # 清理HTML内容
                    content = self.clean_html_content(content)
                    logger.info(f"DrissionPage页面抓取成功: {url}, 内容长度: {len(content)}")
                    return content
                else:
                    logger.warning(f"DrissionPage页面抓取失败: {url}")

            except Exception as e:
                logger.error(f"DrissionPage页面抓取异常: {e}, URL: {url}")

        logger.error(f"所有重试方案均失败: {url}")
        return None
    
    def fetch_page_with_soup(self, url: str) -> Optional[BeautifulSoup]:
        """
        抓取网页并返回BeautifulSoup对象
        
        Args:
            url: 目标URL
            
        Returns:
            BeautifulSoup对象，失败返回None
        """
        html_content = self.fetch_page(url)
        if html_content:
            try:
                soup = BeautifulSoup(html_content, 'html.parser')
                return soup
            except Exception as e:
                logger.error(f"HTML解析失败: {e}, URL: {url}")
                return None
        return None

    def fetch_page_with_js(self, url: str, wait_selector: str = None, wait_time: float = 5.0) -> Optional[str]:
        """
        专门抓取需要JavaScript渲染的页面
        直接使用DrissionPage，适用于已知需要JS渲染的页面

        Args:
            url: 目标URL
            wait_selector: 等待的CSS选择器
            wait_time: 最大等待时间（秒）

        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"使用DrissionPage抓取JS渲染页面: {url}")

        try:
            drission_scraper = self._get_drission_scraper()
            content = drission_scraper.fetch_page_with_js_wait(url, wait_selector, wait_time)

            if content:
                # 清理HTML内容
                content = self.clean_html_content(content)
                logger.info(f"DrissionPage JS页面抓取成功: {url}, 内容长度: {len(content)}")
                return content
            else:
                logger.warning(f"DrissionPage JS页面抓取失败: {url}")
                return None

        except Exception as e:
            logger.error(f"DrissionPage JS页面抓取异常: {e}, URL: {url}")
            return None

    def extract_links(self, html_content: str, base_url: str) -> list:
        """
        从HTML内容中提取所有链接
        
        Args:
            html_content: HTML内容
            base_url: 基础URL，用于处理相对链接
            
        Returns:
            链接列表
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for a_tag in soup.find_all('a', href=True):
                href = a_tag['href']
                text = a_tag.get_text(strip=True)
                
                # 处理相对链接
                if href.startswith('http'):
                    absolute_url = href
                else:
                    absolute_url = urljoin(base_url, href)
                
                links.append({
                    'url': absolute_url,
                    'text': text,
                    'href': href
                })
            
            logger.info(f"从页面提取到 {len(links)} 个链接")
            return links
            
        except Exception as e:
            logger.error(f"链接提取失败: {e}")
            return []
    
    def clean_html_content(self, html_content: str) -> str:
        """
        清理HTML内容，移除脚本、样式等不必要的内容
        
        Args:
            html_content: 原始HTML内容
            
        Returns:
            清理后的HTML内容
        """
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 移除脚本和样式标签
            for tag in soup(['script', 'style', 'meta', 'link']):
                tag.decompose()
            
            # 移除注释
            from bs4 import Comment
            for comment in soup.find_all(string=lambda text: isinstance(text, Comment)):
                comment.extract()
            
            # 返回清理后的HTML
            cleaned_html = str(soup)
            logger.debug(f"HTML内容清理完成，原长度: {len(html_content)}, 清理后长度: {len(cleaned_html)}")
            
            return cleaned_html
            
        except Exception as e:
            logger.error(f"HTML清理失败: {e}")
            return html_content
    
    def is_valid_url(self, url: str) -> bool:
        """
        检查URL是否有效
        
        Args:
            url: 要检查的URL
            
        Returns:
            URL是否有效
        """
        try:
            result = urlparse(url)
            return all([result.scheme, result.netloc])
        except Exception:
            return False

    def close(self):
        """关闭所有资源"""
        if self._drission_scraper:
            try:
                self._drission_scraper.close()
                self._drission_scraper = None
                logger.info("DrissionPage资源已清理")
            except Exception as e:
                logger.warning(f"清理DrissionPage资源时出现警告: {e}")

    def __del__(self):
        """析构函数，确保资源被清理"""
        self.close()
