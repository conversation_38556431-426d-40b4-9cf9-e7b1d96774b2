"""
测试数据验证器
"""
import unittest
from src.utils.validators import DataValidator, validate_input_data, ValidationError

class TestDataValidator(unittest.TestCase):
    """数据验证器测试类"""
    
    def test_validate_url_valid(self):
        """测试有效URL验证"""
        valid_urls = [
            'https://www.example.com',
            'http://example.com',
            'https://subdomain.example.com/path',
            'https://example.com:8080/path?query=value'
        ]
        
        for url in valid_urls:
            with self.subTest(url=url):
                self.assertTrue(DataValidator.validate_url(url))
    
    def test_validate_url_invalid(self):
        """测试无效URL验证"""
        invalid_urls = [
            '',
            'not-a-url',
            'ftp://example.com',  # 不支持的协议
            'example.com',  # 缺少协议
            'https://',  # 缺少域名
        ]
        
        for url in invalid_urls:
            with self.subTest(url=url):
                self.assertFalse(DataValidator.validate_url(url))
    
    def test_validate_company_name_valid(self):
        """测试有效公司名称验证"""
        valid_names = [
            '苹果公司',
            'Apple Inc.',
            '中国移动通信集团有限公司',
            'Microsoft Corporation',
            '阿里巴巴集团控股有限公司',
            'Google LLC'
        ]
        
        for name in valid_names:
            with self.subTest(name=name):
                self.assertTrue(DataValidator.validate_company_name(name))
    
    def test_validate_company_name_invalid(self):
        """测试无效公司名称验证"""
        invalid_names = [
            '',
            'A',  # 太短
            'A' * 101,  # 太长
            None,
            123,  # 不是字符串
            '公司@#$%^&*',  # 包含特殊字符
        ]
        
        for name in invalid_names:
            with self.subTest(name=name):
                self.assertFalse(DataValidator.validate_company_name(name))
    
    def test_validate_xpath_valid(self):
        """测试有效XPath验证"""
        valid_xpaths = [
            '//div[@class="news"]',
            '//a[@href]',
            '/html/body/div[1]//a',
            '//ul[@id="news-list"]//li//a[@href]'
        ]
        
        for xpath in valid_xpaths:
            with self.subTest(xpath=xpath):
                self.assertTrue(DataValidator.validate_xpath(xpath))
    
    def test_validate_xpath_invalid(self):
        """测试无效XPath验证"""
        invalid_xpaths = [
            '',
            'not-xpath',
            'div[@class="news"]',  # 缺少//或/
            None,
            123
        ]
        
        for xpath in invalid_xpaths:
            with self.subTest(xpath=xpath):
                self.assertFalse(DataValidator.validate_xpath(xpath))
    
    def test_sanitize_company_name(self):
        """测试公司名称清理"""
        test_cases = [
            ('  苹果公司  ', '苹果公司'),
            ('Apple   Inc.', 'Apple Inc.'),
            ('公司@#$名称', '公司名称'),
            ('', ''),
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input_name=input_name):
                result = DataValidator.sanitize_company_name(input_name)
                self.assertEqual(result, expected)
    
    def test_normalize_url(self):
        """测试URL标准化"""
        test_cases = [
            ('https://example.com', 'https://example.com'),
            ('//example.com', 'https://example.com'),
            ('example.com', 'https://example.com'),
            ('https://example.com#section', 'https://example.com'),
        ]
        
        for input_url, expected in test_cases:
            with self.subTest(input_url=input_url):
                result = DataValidator.normalize_url(input_url)
                self.assertEqual(result, expected)
    
    def test_validate_input_data_success(self):
        """测试输入数据验证成功"""
        data = {'company_name': '苹果公司'}
        schema = {
            'company_name': {
                'required': True,
                'type': str,
                'validator': DataValidator.validate_company_name
            }
        }
        
        # 应该不抛出异常
        validate_input_data(data, schema)
    
    def test_validate_input_data_missing_required(self):
        """测试输入数据验证缺少必需字段"""
        data = {}
        schema = {
            'company_name': {
                'required': True,
                'type': str
            }
        }
        
        with self.assertRaises(ValidationError):
            validate_input_data(data, schema)
    
    def test_validate_input_data_wrong_type(self):
        """测试输入数据验证类型错误"""
        data = {'company_name': 123}
        schema = {
            'company_name': {
                'required': True,
                'type': str
            }
        }
        
        with self.assertRaises(ValidationError):
            validate_input_data(data, schema)

if __name__ == '__main__':
    unittest.main()
