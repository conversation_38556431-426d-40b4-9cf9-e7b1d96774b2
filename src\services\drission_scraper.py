"""
DrissionPage网页抓取服务
用于处理有防御机制的网站
"""
import time
from typing import Optional
from DrissionPage import ChromiumPage, ChromiumOptions
from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(__name__)

class DrissionScraperService:
    """DrissionPage网页抓取服务类"""
    
    def __init__(self):
        """初始化DrissionPage抓取服务"""
        self.timeout = Config.SCRAPING_TIMEOUT
        self.request_delay = Config.REQUEST_DELAY
        self._page = None
        
        logger.info("DrissionPage抓取服务初始化完成")
    
    def _get_page(self) -> ChromiumPage:
        """
        获取或创建ChromiumPage实例
        
        Returns:
            ChromiumPage实例
        """
        if self._page is None:
            try:
                # 配置浏览器选项
                options = ChromiumOptions()
                
                # 设置无头模式
                options.headless(True)
                
                # 设置用户代理
                options.set_user_agent(
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 '
                    '(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
                )
                
                # 禁用图片加载以提高速度
                options.set_argument('--disable-images')
                
                # 禁用JavaScript（可选，根据需要调整）
                # options.set_argument('--disable-javascript')
                
                # 设置窗口大小
                options.set_argument('--window-size=1920,1080')
                
                # 禁用GPU加速
                options.set_argument('--disable-gpu')
                
                # 禁用沙盒模式
                options.set_argument('--no-sandbox')
                
                # 禁用开发者工具
                options.set_argument('--disable-dev-shm-usage')
                
                # 设置语言
                options.set_argument('--lang=zh-CN')
                
                # 创建页面实例
                self._page = ChromiumPage(addr_or_opts=options)
                
                # 设置页面超时
                self._page.set.timeouts(base=self.timeout)
                
                logger.info("ChromiumPage实例创建成功")
                
            except Exception as e:
                logger.error(f"创建ChromiumPage实例失败: {e}")
                raise
        
        return self._page
    
    def fetch_page(self, url: str, wait_time: float = 2.0) -> Optional[str]:
        """
        使用DrissionPage抓取网页内容
        
        Args:
            url: 目标URL
            wait_time: 页面加载等待时间（秒）
            
        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"使用DrissionPage开始抓取页面: {url}")
        
        try:
            page = self._get_page()
            
            # 访问页面
            page.get(url)
            
            # 等待页面加载
            time.sleep(wait_time)
            
            # 等待页面完全加载（可选）
            try:
                page.wait.load_start()
                page.wait.doc_loaded()
            except Exception as e:
                logger.warning(f"等待页面加载时出现警告: {e}")
            
            # 获取页面HTML内容
            html_content = page.html
            
            if html_content:
                logger.info(f"DrissionPage页面抓取成功: {url}, 内容长度: {len(html_content)}")
                return html_content
            else:
                logger.warning(f"DrissionPage获取到空内容: {url}")
                return None
                
        except Exception as e:
            logger.error(f"DrissionPage抓取页面失败: {e}, URL: {url}")
            return None
    
    def fetch_page_with_js_wait(self, url: str, wait_selector: str = None, wait_time: float = 5.0) -> Optional[str]:
        """
        抓取需要JavaScript渲染的页面
        
        Args:
            url: 目标URL
            wait_selector: 等待的CSS选择器
            wait_time: 最大等待时间（秒）
            
        Returns:
            网页HTML内容，失败返回None
        """
        logger.info(f"使用DrissionPage抓取JS渲染页面: {url}")
        
        try:
            page = self._get_page()
            
            # 访问页面
            page.get(url)
            
            # 如果指定了等待选择器，等待元素出现
            if wait_selector:
                try:
                    page.wait.ele_loaded(wait_selector, timeout=wait_time)
                    logger.info(f"等待元素加载完成: {wait_selector}")
                except Exception as e:
                    logger.warning(f"等待元素超时: {wait_selector}, 错误: {e}")
            else:
                # 默认等待页面加载
                time.sleep(wait_time)
            
            # 获取页面HTML内容
            html_content = page.html
            
            if html_content:
                logger.info(f"DrissionPage JS页面抓取成功: {url}, 内容长度: {len(html_content)}")
                return html_content
            else:
                logger.warning(f"DrissionPage获取到空内容: {url}")
                return None
                
        except Exception as e:
            logger.error(f"DrissionPage抓取JS页面失败: {e}, URL: {url}")
            return None
    
    def close(self):
        """关闭浏览器实例"""
        if self._page:
            try:
                self._page.quit()
                self._page = None
                logger.info("DrissionPage浏览器实例已关闭")
            except Exception as e:
                logger.warning(f"关闭DrissionPage浏览器实例时出现警告: {e}")
    
    def __del__(self):
        """析构函数，确保浏览器实例被关闭"""
        self.close()
