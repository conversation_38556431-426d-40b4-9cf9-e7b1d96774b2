# DrissionPage备用方案使用指南

## 概述

本项目现已集成 DrissionPage 作为网页抓取的备用方案，用于处理有防御机制的网站。当传统的 requests 库无法成功访问页面时，系统会自动切换到 DrissionPage 进行重试。

## 功能特性

### 1. 三层重试策略

- **第1-2次重试**: 使用 requests 库进行常规HTTP请求
- **第3次重试**: 使用 DrissionPage 作为备用方案，支持：
  - JavaScript 渲染
  - 反爬虫机制绕过
  - 真实浏览器环境模拟

### 2. 自动切换机制

系统会在以下情况自动启用 DrissionPage：
- requests 请求超时
- 服务器返回错误状态码（403、503等）
- 网络连接异常
- 其他 requests 相关异常

### 3. 资源管理

- 延迟初始化：只有在需要时才创建浏览器实例
- 自动清理：使用完毕后自动关闭浏览器进程
- 内存优化：支持手动资源清理

## 使用方法

### 基本用法

```python
from src.services.web_scraper import WebScraperService

# 创建抓取服务实例
web_scraper = WebScraperService()

# 抓取普通网页（自动重试机制）
content = web_scraper.fetch_page("https://example.com")

# 使用完毕后清理资源
web_scraper.close()
```

### JavaScript渲染页面

对于已知需要JavaScript渲染的页面，可以直接使用专门的方法：

```python
# 抓取需要JS渲染的页面
content = web_scraper.fetch_page_with_js(
    url="https://spa-example.com",
    wait_selector=".content",  # 等待特定元素加载
    wait_time=5.0             # 最大等待时间（秒）
)
```

### 配置参数

可以通过修改 `src/config.py` 来调整重试策略：

```python
# 最大重试次数（建议设置为3以启用DrissionPage备用方案）
MAX_RETRIES = 3

# 请求超时时间
SCRAPING_TIMEOUT = 30

# 重试延迟时间
REQUEST_DELAY = 1
```

## 工作原理

### 重试流程

```
开始抓取页面
    ↓
第1次尝试 (requests)
    ↓ 失败
第2次尝试 (requests)
    ↓ 失败
第3次尝试 (DrissionPage)
    ↓
返回结果或失败
```

### DrissionPage配置

系统自动配置以下浏览器选项：
- 无头模式运行
- 禁用图片加载（提高速度）
- 设置真实用户代理
- 禁用GPU加速
- 中文语言环境

## 最佳实践

### 1. 选择合适的方法

- **普通网站**: 使用 `fetch_page()` 方法，让系统自动处理重试
- **已知JS网站**: 使用 `fetch_page_with_js()` 方法，直接启用浏览器渲染
- **API接口**: 继续使用 requests 库，无需浏览器渲染

### 2. 资源管理

```python
# 推荐的使用模式
web_scraper = WebScraperService()
try:
    # 执行抓取任务
    content1 = web_scraper.fetch_page(url1)
    content2 = web_scraper.fetch_page(url2)
finally:
    # 确保资源被清理
    web_scraper.close()
```

### 3. 错误处理

```python
try:
    content = web_scraper.fetch_page(url)
    if content:
        # 处理成功获取的内容
        process_content(content)
    else:
        # 处理抓取失败的情况
        logger.warning(f"无法抓取页面: {url}")
except Exception as e:
    logger.error(f"抓取异常: {e}")
```

## 性能考虑

### 1. 启动时间

- requests: 几乎无启动时间
- DrissionPage: 首次启动需要2-3秒（创建浏览器实例）

### 2. 内存使用

- requests: 内存占用极小
- DrissionPage: 需要额外50-100MB内存（浏览器进程）

### 3. 速度对比

- requests: 通常1-2秒完成请求
- DrissionPage: 通常3-5秒完成请求（包含页面渲染）

## 故障排除

### 1. DrissionPage初始化失败

可能原因：
- 缺少Chrome浏览器
- 系统权限不足
- 防火墙阻止

解决方案：
```python
# 检查是否可以正常初始化
try:
    from src.services.drission_scraper import DrissionScraperService
    scraper = DrissionScraperService()
    print("DrissionPage初始化成功")
except Exception as e:
    print(f"初始化失败: {e}")
```

### 2. 页面加载不完整

可能原因：
- 等待时间不足
- 网络速度慢
- 页面结构复杂

解决方案：
```python
# 增加等待时间
content = web_scraper.fetch_page_with_js(
    url=url,
    wait_time=10.0  # 增加到10秒
)
```

### 3. 内存泄漏

确保在使用完毕后调用清理方法：
```python
web_scraper.close()  # 手动清理
# 或者使用上下文管理器（如果实现了的话）
```

## 示例代码

完整的使用示例请参考：
- `examples/drission_fallback_demo.py` - 功能演示
- `tests/test_drission_fallback.py` - 单元测试

## 更新日志

- **v1.0**: 集成DrissionPage作为备用方案
- 支持自动重试机制
- 支持JavaScript渲染
- 支持资源自动清理
